//+------------------------------------------------------------------+
//|                    Enhanced Anti-Martingale EA - M1 SCALPING OPTIMIZED |
//|                     Professional M1 Scalping with Advanced Risk Mgmt |
//|                                  Copyright 2025, MetaQuotes Ltd. |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, MetaQuotes Ltd."
#property link "https://www.mql5.com"
#property version "5.00"
#property description "M1 SCALPING OPTIMIZED: Realistic profit targets, reduced signal filtering"
#property description "CRITICAL FIXES: $3-5 base targets, spread awareness, 1000-point BTC distances"

#include <Trade\Trade.mqh>

//+------------------------------------------------------------------+
//| Critical Enum Declarations for Signal Enhancement               |
//+------------------------------------------------------------------+
enum ENUM_SIGNAL_TYPE
{
    SIGNAL_NONE = 0,    // No signal
    SIGNAL_BUY = 1,     // Buy signal
    SIGNAL_SELL = -1    // Sell signal
};

//+------------------------------------------------------------------+
//| Market Regime Classification for Sideways Detection             |
//+------------------------------------------------------------------+
enum ENUM_MARKET_REGIME
{
    MARKET_TRENDING_UP = 1,     // Strong uptrend
    MARKET_TRENDING_DOWN = -1,  // Strong downtrend
    MARKET_RANGING = 0,         // Sideways/ranging market
    MARKET_BREAKOUT = 2,        // Breakout from range
    MARKET_UNCERTAIN = 3        // Uncertain/transitional
};

//+------------------------------------------------------------------+
//| Range Level Structure for Support/Resistance Detection          |
//+------------------------------------------------------------------+
struct RangeLevels
{
    double support;           // Support level
    double resistance;        // Resistance level
    double range_size;        // Size of the range
    double range_midpoint;    // Middle of the range
    bool is_valid_range;      // Whether range is valid for trading
    datetime last_update;     // Last update time
};

//--- Input Parameters (Auto-configured based on symbol detection)
input group "=== TRADING SETTINGS ==="
input double InitialLot = 0.05;                    // Initial lot size (Auto-optimized per instrument)
input double MaxDailyLoss = 0.15;                  // Maximum daily loss (15%)
input bool UseFibonacciProgression = true;         // Use Fibonacci lot progression
input bool EnableContinuousTrading = true;         // Always return BUY/SELL signals
input bool UseMomentumAcceleration = true;         // Accelerate progression on strong trends

//--- Auto-Configuration Variables (Set based on symbol detection)
double g_configuredTakeProfit = 0.005;             // Will be set based on instrument
int g_configuredMaxSimultaneousOrders = 2;         // Will be set based on instrument
int g_configuredMaxAntiMartingaleStep = 4;         // Will be set based on instrument
double g_configuredMomentumThreshold = 0.6;        // Will be set based on instrument



//--- Auto-Configuration Variables for Signal Analysis (Set based on symbol detection)
int g_configuredMinConfluenceSignals = 2;          // Will be set based on instrument
double g_configuredMinTrendStrength = 0.21;        // Will be set based on instrument
bool g_configuredRequireADXConfirmation = false;   // Will be set based on instrument
double g_configuredADXTrendThreshold = 25.0;       // Will be set based on instrument
double g_configuredDailyProfitTarget = 50.0;       // Will be set based on instrument

//--- Legacy Variables (for backward compatibility with existing code)
double TakeProfit = 0.005;                         // Will be set by auto-configuration
int MinConfluenceSignals = 2;                      // Will be set by auto-configuration
double MinTrendStrength = 0.21;                    // Will be set by auto-configuration
bool RequireADXConfirmation = false;               // Will be set by auto-configuration
double ADXTrendThreshold = 25.0;                   // Will be set by auto-configuration
double DailyProfitTarget = 50.0;                   // Will be set by auto-configuration
int MaxSimultaneousOrders = 2;                     // Will be set by auto-configuration
int MaxAntiMartingaleStep = 4;                     // Will be set by auto-configuration

input group "=== ADAPTIVE PAUSE MECHANISM ==="
input bool UseAdaptivePause = true;               // Enable cooling-off mechanism
input int MaxConsecutiveLosses = 3;               // Trigger pause after N consecutive losses
input int PauseMinutes = 30;                      // Pause duration in minutes
input double PauseVolatilityThreshold = 1.5;      // Volatility threshold for pause
input bool ResetSignalThresholds = true;          // Reset confluence requirements after pause

//--- Auto-Configuration Variables for Trailing Profit (Set based on symbol detection)
bool g_configuredUseTrailingProfit = true;         // Will be set based on instrument
double g_configuredTrailingProfitTrigger = 0.5;    // Will be set based on instrument
double g_configuredTrailingProfitStep = 0.30;      // Will be set based on instrument
double g_configuredTrailingProfitMinLock = 0.70;   // Will be set based on instrument
int g_configuredTrailingProfitFrequency = 5;       // Will be set based on instrument
bool g_configuredTrailingProfitOnlyAfterTP = false; // Will be set based on instrument
double g_configuredProfitCushion = 0.3;            // Will be set based on instrument
bool g_configuredUseDynamicTrailing = true;        // Will be set based on instrument
double g_configuredVolatilityMultiplier = 1.5;     // Will be set based on instrument

input group "=== TECHNICAL ANALYSIS ==="
input int MA_Fast_Period = 20;                     // Fast MA period
input int MA_Slow_Period = 50;                     // Slow MA period
input int RSI_Period = 14;                         // RSI period
input int ADX_Period = 14;                         // ADX period
input int MACD_Fast = 12;                          // MACD fast EMA
input int MACD_Slow = 26;                          // MACD slow EMA
input int MACD_Signal = 9;                         // MACD signal line
input int Stoch_K = 5;                             // Stochastic %K
input int Stoch_D = 3;                             // Stochastic %D
input int Stoch_Slowing = 3;                       // Stochastic slowing

input group "=== RISK MANAGEMENT ==="
input double AccountBalance = 0;                   // Account balance (0 = auto)
input bool UseTrailingStop = false;                // Use trailing stop (DISABLED for Anti-Martingale)
input double TrailingStopPips = 10;                // Trailing stop in pips
input bool UseAntiMartingalePrecision = true;      // Use precise SL/TP targets (recommended)

//--- Global Variables
CTrade Trade;

//--- Indicator Handles
int g_maFastHandle = INVALID_HANDLE;
int g_maSlowHandle = INVALID_HANDLE;
int g_rsiHandle = INVALID_HANDLE;
int g_adxHandle = INVALID_HANDLE;
int g_macdHandle = INVALID_HANDLE;
int g_stochHandle = INVALID_HANDLE;
int g_bbHandle = INVALID_HANDLE;

//--- Trading Variables
double g_accountBalance = 0;
double g_dailyStartBalance = 0;
double g_dailyProfit = 0;
double g_targetProfit = 0;
datetime g_lastTradeTime = 0;
datetime g_dailyStartTime = 0;
datetime g_lastLogTime = 0;
int g_activeOrders = 0;
bool g_tradingEnabled = true;

//--- Enhanced Anti-Martingale progression system - PHASE 1 OPTIMIZED (Accelerated Fibonacci)
double g_winSequence[] = {1.0, 1.3, 1.7, 2.2, 2.9, 3.8}; // PHASE 1: Accelerated progression for faster profits
double g_profitSteps[] = {1.0, 1.3, 1.7, 2.2, 2.9, 3.8}; // PHASE 1: Faster profit accumulation (6 steps)
double g_lossSteps[] = {1.0, 1.0, 1.3, 1.7, 2.2, 2.9}; // PHASE 1: Progressive loss protection

//--- ASYMMETRIC 1:3 RISK-REWARD SYSTEM - NEW IMPLEMENTATION
bool g_useAsymmetricRR = true;                             // Enable asymmetric risk-reward system
double g_asymmetricProfitTarget = 6.0;                     // Fixed $6 profit target
double g_asymmetricSLTarget = 18.0;                        // Fixed $18 stop loss (1:3 ratio)
double g_asymmetricWinSequence[] = {1.0, 1.2, 1.5, 1.8, 2.2, 2.6}; // Conservative progression for asymmetric
double g_asymmetricProfitSteps[] = {1.0, 1.2, 1.5, 1.8, 2.2, 2.6}; // Matches win sequence
double g_asymmetricLossSteps[] = {1.0, 0.8, 1.0, 1.2, 1.5, 1.8};   // Reduced loss scaling for protection
int g_consecutiveWins = 0;
int g_consecutiveLosses = 0;
double g_currentWinStreak = 0.0;
double g_maxWinStreak = 0.0;
bool g_isInWinningStreak = false;
int g_currentStep = 0; // Current Anti-Martingale step (0-based)

//--- Trailing Profit System Variables
datetime g_lastTrailingUpdate = 0;
double g_totalTrailingProfit = 0.0;
int g_trailingProfitCount = 0;

//--- Adaptive Pause Mechanism Variables
bool g_isPaused = false;
datetime g_pauseStartTime = 0;
datetime g_pauseEndTime = 0;
int g_pauseCount = 0;
double g_prePauseBalance = 0.0;
int g_originalMinConfluence = 0;
double g_originalMinTrendStrength = 0.0;
int g_currentMinConfluence = 0;
double g_currentMinTrendStrength = 0.0;

//--- Enhanced signal weights and confluence system
struct SignalWeights
{
    double ma_weight;
    double rsi_weight;
    double adx_weight;
    double macd_weight;
    double stoch_weight;
    double bb_weight;
    double momentum_weight;
};

SignalWeights g_weights = {0.35, 0.25, 0.15, 0.10, 0.08, 0.04, 0.03}; // PHASE 2: BTC-optimized weights

//--- Enhanced Signal Processing Variables
double g_currentSignalStrength = 0.0;
double g_confluenceBoost = 0.0;
double g_marketConditionBoost = 0.0;
double g_volatilityBoost = 0.0;
double g_finalEnhancedStrength = 0.0;

//--- Dynamic Risk Management Variables
double g_qualityAdjustedLot = 0.0;
double g_adaptiveSLDistance = 0.0;
double g_adaptiveTPDistance = 0.0;
int g_smartInterval = 30;

//--- Multi-Timeframe Analysis Variables
bool g_m5TrendAlignment = false;
bool g_m15TrendAlignment = false;
bool g_h1TrendAlignment = false;
double g_timeframeConfluenceScore = 0.0;

//--- Performance Tracking Variables
int g_enhancedTradesCount = 0;
int g_enhancedWinsCount = 0;
double g_enhancedTotalProfit = 0.0;
double g_enhancedTotalLoss = 0.0;
double g_averageSignalStrength = 0.0;
double g_signalStrengthSum = 0.0;

//--- Market Regime Detection Variables
ENUM_MARKET_REGIME g_currentMarketRegime = MARKET_UNCERTAIN;
RangeLevels g_currentRange = {0};
double g_regimeConfidence = 0.0;
datetime g_lastRegimeUpdate = 0;

//--- Regime-Based Performance Tracking
int g_trendingTrades = 0;
int g_rangingTrades = 0;
int g_breakoutTrades = 0;
int g_trendingWins = 0;
int g_rangingWins = 0;
int g_breakoutWins = 0;
double g_trendingProfit = 0.0;
double g_rangingProfit = 0.0;
double g_breakoutProfit = 0.0;

//--- Sideways Market Protection Variables
bool g_rangingMarketDetected = false;
int g_consecutiveRangingLosses = 0;
datetime g_lastRangingLoss = 0;
double g_rangingLossThreshold = 3; // Max consecutive losses in ranging markets

//--- ASYMMETRIC RISK MANAGEMENT VARIABLES
struct AsymmetricRiskControl
{
    double maxRiskPerTrade;           // Maximum risk per trade (1.5%)
    double maxTotalExposure;          // Maximum total exposure (5%)
    int maxDailyTrades;               // Maximum trades per day
    double minAccountBalance;         // Minimum account balance required
    int consecutiveAsymmetricLosses;  // Track consecutive losses in asymmetric mode
    double totalAsymmetricRisk;       // Current total risk exposure
    datetime lastAsymmetricTrade;     // Last asymmetric trade time
    bool asymmetricSystemEnabled;     // System enable/disable flag
};

AsymmetricRiskControl g_asymmetricRisk = {0.015, 0.05, 20, 5000.0, 0, 0.0, 0, true};

//--- TIME-BASED POSITION MANAGEMENT SYSTEM
struct TimeBasedManagement
{
    datetime openTime;              // Position open time
    datetime fiveMinuteMark;        // 5-minute checkpoint
    datetime tenMinuteMark;         // 10-minute checkpoint
    datetime fifteenMinuteMark;     // 15-minute checkpoint
    datetime maxCloseTime;          // Maximum position duration (20 minutes)
    bool fiveMinuteChecked;         // Has 5-minute check been performed
    bool tenMinuteChecked;          // Has 10-minute check been performed
    bool fifteenMinuteChecked;      // Has 15-minute check been performed
    double initialProfit;           // Profit at position open
    double fiveMinuteProfit;        // Profit at 5-minute mark
    double tenMinuteProfit;         // Profit at 10-minute mark
    double fifteenMinuteProfit;     // Profit at 15-minute mark
    bool momentumReversalDetected;  // Has momentum reversal been detected
    datetime lastMomentumCheck;     // Last momentum check time
};

// Time-based management configuration
struct TimeBasedConfig
{
    bool enableTimeBasedManagement;     // Enable/disable time-based management
    int maxPositionDuration;            // Maximum position duration in minutes (20)
    double fiveMinuteThreshold;         // Minimum profit at 5 minutes ($1)
    double tenMinuteThreshold;          // Minimum profit at 10 minutes ($2)
    double fifteenMinuteThreshold;      // Minimum profit at 15 minutes ($3)
    int momentumCheckInterval;          // Momentum check interval in seconds (30)
    double momentumReversalThreshold;   // Momentum reversal threshold (0.3)
};

TimeBasedConfig g_timeBasedConfig = {true, 20, 1.0, 2.0, 3.0, 30, 0.3};

//--- Signal confluence tracking
struct SignalConfluence
{
    int bullish_signals;
    int bearish_signals;
    int neutral_signals;
    double total_strength;
    double trend_strength;
    bool is_trending;
};



//--- Enhanced order tracking structure with parallel Anti-Martingale states
struct OrderInfo
{
    ulong ticket;
    int type;
    double lots;
    double openPrice;
    double targetProfit;
    double stopLoss;
    datetime openTime;
    string comment;
    double signalStrength;      // Signal confidence (0.0 to 1.0)
    bool isWinningTrade;        // Track if trade is currently profitable
    double maxProfit;           // Maximum profit achieved
    double maxLoss;             // Maximum loss experienced

    // Individual Anti-Martingale State (each order has its own progression)
    int orderStep;              // This order's Anti-Martingale step (0-based)
    int consecutiveWins;        // Consecutive wins for this order chain
    int consecutiveLosses;      // Consecutive losses for this order chain
    double winStreak;           // Total profit from this order's win streak
    bool isInWinningStreak;     // Is this order part of a winning streak
    double lastTradeProfit;     // Profit from the previous trade in this chain
    int orderChainId;           // Unique ID to track order chains (1, 2, 3)

    // Enhanced time-based management (replaces 30-minute timeout)
    TimeBasedManagement timeManagement; // Time-based management data

    // Trailing Profit System
    bool trailingProfitActive;  // Is trailing profit enabled for this order
    double initialTPTarget;     // Original take profit target amount ($15 base)
    double trailingTPLevel;     // Current trailing take profit level
    double maxProfitAchieved;   // Maximum profit reached (for trailing calculation)
    double lockedProfit;        // Minimum profit locked in
    datetime lastTrailingUpdate; // Last time trailing profit was updated
    int trailingUpdateCount;    // Number of trailing updates
};

//--- Trade outcome tracking
struct TradeOutcome
{
    bool isWin;
    double profit;
    double signalStrength;
    datetime closeTime;
    int consecutiveWins;
    int consecutiveLosses;
    double trailingProfitGained; // Additional profit from trailing
};

OrderInfo g_orders[];
TradeOutcome g_recentTrades[10]; // Track last 10 trades for analysis
int g_orderCount = 0;
int g_tradeHistoryIndex = 0;

//--- Parallel Order Chain Management (3 simultaneous order chains)
struct OrderChain
{
    int chainId;                // Chain identifier (1, 2, 3)
    int currentStep;            // Current Anti-Martingale step for this chain
    int consecutiveWins;        // Consecutive wins in this chain
    int consecutiveLosses;      // Consecutive losses in this chain
    double winStreak;           // Total profit from wins in this chain
    double lastTradeProfit;     // Last trade profit in this chain
    bool isInWinningStreak;     // Is this chain in a winning streak
    bool isActive;              // Is this chain currently active (has open order)
    ulong activeTicket;         // Current active order ticket for this chain
    datetime lastTradeTime;     // Last trade time for this chain
};

OrderChain g_orderChains[3];    // Three parallel order chains
int g_nextChainId = 1;          // Next available chain ID (1-3, cycles)

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Force M1 timeframe for scalping
    if(Period() != PERIOD_M1)
    {
        LogMessage("ERROR: EA must run on M1 timeframe for optimal scalping performance");
        return(INIT_FAILED);
    }

    // Initialize account balance
    g_accountBalance = (AccountBalance == 0) ? AccountInfoDouble(ACCOUNT_BALANCE) : AccountBalance;
    g_dailyStartBalance = g_accountBalance;
    g_targetProfit = g_accountBalance * TakeProfit;
    g_dailyStartTime = TimeCurrent();

    // Initialize indicator handles
    if(!InitializeIndicators())
    {
        LogMessage("ERROR: Failed to initialize indicators");
        return(INIT_FAILED);
    }

    // Configure trade settings
    Trade.SetDeviationInPoints(30);  // Increased for volatile markets
    Trade.SetTypeFilling(ORDER_FILLING_IOC);  // Better for scalping
    Trade.SetAsyncMode(false);

    // Initialize order tracking and Anti-Martingale system
    ArrayResize(g_orders, 0);
    g_orderCount = 0;
    g_consecutiveWins = 0;
    g_consecutiveLosses = 0;
    g_currentWinStreak = 0.0;
    g_maxWinStreak = 0.0;
    g_isInWinningStreak = false;
    g_currentStep = 0; // Start at step 1 (index 0)
    g_tradeHistoryIndex = 0;

    // Initialize parallel order chains (3 chains for simultaneous orders)
    for(int i = 0; i < 3; i++)
    {
        g_orderChains[i].chainId = i + 1;
        g_orderChains[i].currentStep = 0;
        g_orderChains[i].consecutiveWins = 0;
        g_orderChains[i].consecutiveLosses = 0;
        g_orderChains[i].winStreak = 0.0;
        g_orderChains[i].lastTradeProfit = 0.0;
        g_orderChains[i].isInWinningStreak = false;
        g_orderChains[i].isActive = false;
        g_orderChains[i].activeTicket = 0;
        g_orderChains[i].lastTradeTime = 0;
    }
    g_nextChainId = 1;

    // Initialize trailing profit system
    g_lastTrailingUpdate = 0;
    g_totalTrailingProfit = 0.0;
    g_trailingProfitCount = 0;

    // Initialize adaptive pause mechanism
    g_isPaused = false;
    g_pauseStartTime = 0;
    g_pauseEndTime = 0;
    g_pauseCount = 0;
    g_prePauseBalance = 0.0;
    g_originalMinConfluence = MinConfluenceSignals;
    g_originalMinTrendStrength = MinTrendStrength;
    g_currentMinConfluence = MinConfluenceSignals;
    g_currentMinTrendStrength = MinTrendStrength;

    // Initialize trade history
    for(int i = 0; i < 10; i++)
    {
        g_recentTrades[i].isWin = false;
        g_recentTrades[i].profit = 0.0;
        g_recentTrades[i].signalStrength = 0.0;
        g_recentTrades[i].closeTime = 0;
        g_recentTrades[i].consecutiveWins = 0;
        g_recentTrades[i].consecutiveLosses = 0;
        g_recentTrades[i].trailingProfitGained = 0.0;
    }

    // Essential initialization logging
    LogMessage("=== ANTI-MARTINGALE EA INITIALIZED ===");
    LogMessage(StringFormat("Account: $%.2f | Daily Target: $%.0f | Max Loss: %.1f%%",
              g_accountBalance, g_configuredDailyProfitTarget, MaxDailyLoss * 100));
    LogMessage(StringFormat("Initial Lot: %.3f | Max Orders: %d | Max Steps: %d",
              InitialLot, g_configuredMaxSimultaneousOrders, g_configuredMaxAntiMartingaleStep));

    // Auto-configure parameters based on symbol detection
    ConfigureParametersForSymbol();

    // Initialize asymmetric risk-reward system
    InitializeAsymmetricSystem();

    // Test Anti-Martingale progression (for debugging)
    TestAntiMartingaleProgression();

    // CRITICAL: Verify M1 scalping optimizations
    VerifyM1ScalpingOptimizations();

    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Auto-configure parameters based on symbol detection             |
//+------------------------------------------------------------------+
void ConfigureParametersForSymbol()
{
    string symbol = Symbol();

    LogMessage(StringFormat("Auto-Config: %s detected", symbol));

    if(StringFind(symbol, "BTC") >= 0)
    {
        // BTC/USD Optimized Configuration
        g_configuredTakeProfit = 0.008;                    // Higher profit target for BTC volatility
        g_configuredMaxSimultaneousOrders = 3;             // Conservative for high volatility
        g_configuredMaxAntiMartingaleStep = 5;             // Reduced steps for BTC
        g_configuredMomentumThreshold = 0.7;               // Higher threshold for BTC

        // Trailing Profit Settings for BTC
        g_configuredUseTrailingProfit = true;
        g_configuredTrailingProfitTrigger = 0.6;           // Higher trigger for BTC
        g_configuredTrailingProfitStep = 0.25;             // More conservative trailing
        g_configuredTrailingProfitMinLock = 0.75;          // Higher lock percentage
        g_configuredTrailingProfitFrequency = 3;           // Faster updates for BTC
        g_configuredTrailingProfitOnlyAfterTP = true;
        g_configuredProfitCushion = 0.4;                   // Higher cushion for BTC
        g_configuredUseDynamicTrailing = true;
        g_configuredVolatilityMultiplier = 1.8;            // Higher volatility response

        // Signal Analysis for BTC - M1 SCALPING OPTIMIZED
        g_configuredMinConfluenceSignals = 0;              // CRITICAL: Allow single strong signals for M1 scalping
        g_configuredMinTrendStrength = 0.05;               // CRITICAL: Much lower for M1 micro-trends
        g_configuredRequireADXConfirmation = false;        // CRITICAL: Disabled for faster M1 entries
        g_configuredADXTrendThreshold = 22.0;
        g_configuredDailyProfitTarget = 500.0;             // Higher target for BTC



        // Update legacy variables for backward compatibility
        TakeProfit = g_configuredTakeProfit;
        MinConfluenceSignals = g_configuredMinConfluenceSignals;
        MinTrendStrength = g_configuredMinTrendStrength;
        RequireADXConfirmation = g_configuredRequireADXConfirmation;
        ADXTrendThreshold = g_configuredADXTrendThreshold;
        DailyProfitTarget = g_configuredDailyProfitTarget;
        MaxSimultaneousOrders = g_configuredMaxSimultaneousOrders;
        MaxAntiMartingaleStep = g_configuredMaxAntiMartingaleStep;

        LogMessage("✅ BTC Config: 0.8% targets, 3 parallel orders, 5 steps");
    }
    else if(StringFind(symbol, "XAU") >= 0 || StringFind(symbol, "GOLD") >= 0)
    {
        // XAU/USD Optimized Configuration
        g_configuredTakeProfit = 0.006;                    // Moderate profit target for gold
        g_configuredMaxSimultaneousOrders = 3;             // Standard for gold
        g_configuredMaxAntiMartingaleStep = 4;             // Full steps for gold
        g_configuredMomentumThreshold = 0.5;               // Lower threshold for gold

        // Trailing Profit Settings for XAU
        g_configuredUseTrailingProfit = true;
        g_configuredTrailingProfitTrigger = 0.5;           // Standard trigger for XAU
        g_configuredTrailingProfitStep = 0.30;             // Balanced trailing
        g_configuredTrailingProfitMinLock = 0.70;          // Standard lock percentage
        g_configuredTrailingProfitFrequency = 5;           // Standard updates for XAU
        g_configuredTrailingProfitOnlyAfterTP = true;
        g_configuredProfitCushion = 0.3;                   // Standard cushion for XAU
        g_configuredUseDynamicTrailing = true;
        g_configuredVolatilityMultiplier = 1.5;            // Standard volatility response

        // Signal Analysis for XAU - M1 SCALPING OPTIMIZED
        g_configuredMinConfluenceSignals = 0;              // CRITICAL: Allow single strong signals for M1 scalping
        g_configuredMinTrendStrength = 0.05;               // CRITICAL: Much lower for M1 micro-trends
        g_configuredRequireADXConfirmation = false;        // CRITICAL: Disabled for faster M1 entries
        g_configuredADXTrendThreshold = 20.0;
        g_configuredDailyProfitTarget = 500.0;              // Moderate target for XAU



        // Update legacy variables for backward compatibility
        TakeProfit = g_configuredTakeProfit;
        MinConfluenceSignals = g_configuredMinConfluenceSignals;
        MinTrendStrength = g_configuredMinTrendStrength;
        RequireADXConfirmation = g_configuredRequireADXConfirmation;
        ADXTrendThreshold = g_configuredADXTrendThreshold;
        DailyProfitTarget = g_configuredDailyProfitTarget;
        MaxSimultaneousOrders = g_configuredMaxSimultaneousOrders;
        MaxAntiMartingaleStep = g_configuredMaxAntiMartingaleStep;

        LogMessage("✅ XAU Config: 0.6% targets, 3 parallel orders, 4 steps");
    }
    else
    {
        // Default Configuration for other instruments
        g_configuredTakeProfit = 0.005;                    // Standard profit target
        g_configuredMaxSimultaneousOrders = 3;             // 3 parallel orders
        g_configuredMaxAntiMartingaleStep = 4;             // Full steps
        g_configuredMomentumThreshold = 0.6;               // Standard threshold

        // Standard Trailing Profit Settings
        g_configuredUseTrailingProfit = true;
        g_configuredTrailingProfitTrigger = 0.5;
        g_configuredTrailingProfitStep = 0.30;
        g_configuredTrailingProfitMinLock = 0.70;
        g_configuredTrailingProfitFrequency = 5;
        g_configuredTrailingProfitOnlyAfterTP = false;
        g_configuredProfitCushion = 0.3;
        g_configuredUseDynamicTrailing = true;
        g_configuredVolatilityMultiplier = 1.5;

        // Standard Signal Analysis - M1 SCALPING OPTIMIZED
        g_configuredMinConfluenceSignals = 0;              // CRITICAL: Allow single strong signals for M1 scalping
        g_configuredMinTrendStrength = 0.05;               // CRITICAL: Much lower for M1 micro-trends
        g_configuredRequireADXConfirmation = false;        // CRITICAL: Disabled for faster M1 entries
        g_configuredADXTrendThreshold = 25.0;
        g_configuredDailyProfitTarget = 50.0;



        // Update legacy variables for backward compatibility
        TakeProfit = g_configuredTakeProfit;
        MinConfluenceSignals = g_configuredMinConfluenceSignals;
        MinTrendStrength = g_configuredMinTrendStrength;
        RequireADXConfirmation = g_configuredRequireADXConfirmation;
        ADXTrendThreshold = g_configuredADXTrendThreshold;
        DailyProfitTarget = g_configuredDailyProfitTarget;
        MaxSimultaneousOrders = g_configuredMaxSimultaneousOrders;
        MaxAntiMartingaleStep = g_configuredMaxAntiMartingaleStep;

        LogMessage("✅ Default Config: 0.5% targets, 3 parallel orders, 4 steps");
    }
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    static bool isInitialized = false;
    static int tickCount = 0;
    static datetime lastTradeCheck = 0;

    tickCount++;

    // Allow some ticks for indicators to initialize
    if(!isInitialized && tickCount < 10)
        return;

    if(!isInitialized)
    {
        isInitialized = true;
        LogMessage("EA Ready - Indicators initialized");
    }

    // Update daily profit tracking
    UpdateDailyProfit();

    // Check daily loss limit (15% protection)
    if(CheckDailyLossLimit())
    {
        LogMessage("DAILY LOSS LIMIT REACHED - Trading disabled for today");
        g_tradingEnabled = false;
        CloseAllPositions();
        return;
    }

    // Check daily profit target (using configured value)
    if(g_dailyProfit >= g_configuredDailyProfitTarget)
    {
        LogMessage(StringFormat("DAILY PROFIT TARGET ACHIEVED: %.2f", g_dailyProfit));
        g_tradingEnabled = false;
        CloseAllPositions();
        return;
    }

    // Skip trading if disabled
    if(!g_tradingEnabled)
        return;

    // Check adaptive pause mechanism
    datetime currentTime = TimeCurrent();
    if(UseAdaptivePause)
    {
        CheckAdaptivePause();
        if(g_isPaused)
        {
            LogMessage(StringFormat("⏸️ TRADING PAUSED: %d minutes remaining",
                      (int)((g_pauseEndTime - currentTime) / 60)));
            return;
        }
    }

    // Update order tracking every tick
    UpdateOrderTracking();

    // Monitor existing positions every tick for best profit optimization
    MonitorPositions();

    // Apply trailing profit system every tick (with frequency control)
    if(g_configuredUseTrailingProfit)
    {
        ApplyTrailingProfitSystem();
    }

    // Check for new trades - PHASE 1 OPTIMIZED: Dynamic timing intervals based on volatility
    bool shouldCheckTrade = false;

    // PHASE 1: Calculate adaptive interval based on ATR volatility
    int adaptiveInterval = CalculateAdaptiveInterval();

    // OPTIMIZED: Allow new trades with dynamic intervals for more opportunities with parallel chains
    if(g_activeOrders < g_configuredMaxSimultaneousOrders)
    {
        // Check if any chain is available and enough time has passed
        int availableChain = GetNextAvailableChain();
        if(availableChain > 0)
        {
            int chainIndex = availableChain - 1;
            // Calculate regime-based smart interval
            int regimeInterval = adaptiveInterval; // Default from existing CalculateAdaptiveInterval()

            if(g_currentSignalStrength > 0)
            {
                regimeInterval = CalculateRegimeBasedInterval(g_currentMarketRegime, g_currentSignalStrength);
                g_smartInterval = regimeInterval;

                // Check if optimal entry time (enhanced with regime awareness)
                if(!IsOptimalEntryTime())
                {
                    LogMessage("⏳ DELAYING ENTRY: Market conditions not optimal");
                    //continue; // Skip this chain for now
                }

                // Additional regime-specific checks
                if(g_currentMarketRegime == MARKET_RANGING && g_consecutiveRangingLosses >= 2)
                {
                    LogMessage("⏳ RANGING PROTECTION: Delaying entry due to recent ranging losses");
                    return; // Skip trading for ranging protection
                }
            }

            if(currentTime - g_orderChains[chainIndex].lastTradeTime >= regimeInterval)
            {
                shouldCheckTrade = true;

                // Enhanced logging with regime interval info
                string regimeName = (g_currentMarketRegime == MARKET_RANGING) ? "RANGING" :
                                   (g_currentMarketRegime == MARKET_TRENDING_UP) ? "TRENDING_UP" :
                                   (g_currentMarketRegime == MARKET_TRENDING_DOWN) ? "TRENDING_DOWN" :
                                   (g_currentMarketRegime == MARKET_BREAKOUT) ? "BREAKOUT" : "UNCERTAIN";

                LogMessage(StringFormat("Chain %d available: %d seconds since last trade (regime interval: %d, signal: %.3f, regime: %s)",
                          availableChain, (int)(currentTime - g_orderChains[chainIndex].lastTradeTime),
                          regimeInterval, g_currentSignalStrength, regimeName));

            lastTradeCheck = currentTime;
            }
        }

        // Exception: Allow immediate entry only if no orders AND more than 60 seconds since EA start
        if(g_activeOrders == 0 && currentTime - g_lastTradeTime >= 60)
        {
            shouldCheckTrade = true;
            lastTradeCheck = currentTime;
            LogMessage("No active orders - immediate entry allowed (60s+ since last trade)");
        }
    }

    if(shouldCheckTrade)
    {
        // Get market signal with strength (always BUY or SELL, never NONE)
        double signalStrength = 0.0;
        int signal = GetMarketSignal(signalStrength);

        LogMessage(StringFormat("Market signal: %s (Strength: %.2f)",
                  (signal == ORDER_TYPE_BUY) ? "BUY" : "SELL", signalStrength));

        // Execute trading logic with signal strength
        ExecuteTradeSignal(signal, signalStrength);
    }

    // Log status every 5 minutes (only if we have active orders or significant changes)
    if(currentTime - g_lastLogTime >= 300)
    {
        if(g_activeOrders > 0 || g_dailyProfit != 0 || g_consecutiveWins > 0 || g_consecutiveLosses > 0)
        {
            LogTradingStatus();
        }
        g_lastLogTime = currentTime;
    }
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Release all indicator handles
    if(g_maFastHandle != INVALID_HANDLE) IndicatorRelease(g_maFastHandle);
    if(g_maSlowHandle != INVALID_HANDLE) IndicatorRelease(g_maSlowHandle);
    if(g_rsiHandle != INVALID_HANDLE) IndicatorRelease(g_rsiHandle);
    if(g_adxHandle != INVALID_HANDLE) IndicatorRelease(g_adxHandle);
    if(g_macdHandle != INVALID_HANDLE) IndicatorRelease(g_macdHandle);
    if(g_stochHandle != INVALID_HANDLE) IndicatorRelease(g_stochHandle);
    if(g_bbHandle != INVALID_HANDLE) IndicatorRelease(g_bbHandle);

    LogMessage("=== EA DEINITIALIZED ===");
    LogMessage(StringFormat("Final Daily P&L: %.2f", g_dailyProfit));
    LogMessage(StringFormat("Total Orders Executed: %d", g_orderCount));
    LogMessage(StringFormat("Total Trailing Profit Gained: %.2f", g_totalTrailingProfit));
    LogMessage(StringFormat("Trailing Profit Operations: %d", g_trailingProfitCount));
}

//+------------------------------------------------------------------+
//| Advanced Trailing Profit System - Hybrid Fibonacci-Percentage  |
//+------------------------------------------------------------------+
void ApplyTrailingProfitSystem()
{
    datetime currentTime = TimeCurrent();

    // Control update frequency (using configured value)
    if(currentTime - g_lastTrailingUpdate < g_configuredTrailingProfitFrequency)
        return;

    g_lastTrailingUpdate = currentTime;

    for(int i = 0; i < ArraySize(g_orders); i++)
    {
        if(PositionSelectByTicket(g_orders[i].ticket))
        {
            // Apply time-based management first (may close position)
            bool positionClosed = ApplyTimeBasedManagement(i);

            // If position wasn't closed by time-based management, apply trailing profit
            if(!positionClosed && PositionSelectByTicket(g_orders[i].ticket))
            {
                ApplyTrailingProfitToPosition(i);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Apply trailing profit to individual position                     |
//+------------------------------------------------------------------+
void ApplyTrailingProfitToPosition(int orderIndex)
{
    if(!PositionSelectByTicket(g_orders[orderIndex].ticket))
        return;

    double currentProfit = PositionGetDouble(POSITION_PROFIT);
    double currentTP = PositionGetDouble(POSITION_TP);
    double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
    int positionType = (int)PositionGetInteger(POSITION_TYPE);

    // Initialize trailing profit data if not set
    if(g_orders[orderIndex].initialTPTarget == 0)
    {
        InitializeTrailingProfitData(orderIndex, currentProfit);
    }

    // Check if trailing profit should be activated
    if(!g_orders[orderIndex].trailingProfitActive)
    {
        if(ShouldActivateTrailingProfit(orderIndex, currentProfit))
        {
            ActivateTrailingProfit(orderIndex, currentProfit);
        }
        else
        {
            // Debug: Show why trailing profit is not activated (only every 30 seconds to avoid spam)
            datetime currentTime = TimeCurrent();
            if(currentTime - g_orders[orderIndex].lastTrailingUpdate >= 30)
            {
                double targetAmount = g_orders[orderIndex].initialTPTarget;
                double triggerLevel = targetAmount * g_configuredTrailingProfitTrigger;
                //LogMessage(StringFormat("🔍 Trailing Debug: Ticket %d, Profit: $%.2f, Target: $%.2f, Trigger: $%.2f",
                          //g_orders[orderIndex].ticket, currentProfit, targetAmount, triggerLevel));
                g_orders[orderIndex].lastTrailingUpdate = currentTime;
            }
        }
        return; // Don't trail until activated
    }

    // Update maximum profit achieved
    if(currentProfit > g_orders[orderIndex].maxProfitAchieved)
    {
        g_orders[orderIndex].maxProfitAchieved = currentProfit;
    }

    // Calculate new trailing TP level
    double newTPLevel = CalculateTrailingTPLevel(orderIndex, currentProfit);

    // Check if TP should be updated
    if(ShouldUpdateTrailingTP(orderIndex, newTPLevel, currentTP, positionType))
    {
        UpdateTrailingTP(orderIndex, newTPLevel, positionType, openPrice);
    }
}

//+------------------------------------------------------------------+
//| Initialize trailing profit data for new position                 |
//+------------------------------------------------------------------+
void InitializeTrailingProfitData(int orderIndex, double currentProfit)
{
    // CRITICAL: Calculate realistic initial TP target for M1 scalping
    string symbol = Symbol();
    int stepIndex = MathMin(g_currentStep, ArraySize(g_profitSteps) - 1);

    // CRITICAL: Use smaller, realistic base targets for M1 scalping
    double baseTarget = 3.0; // Default $3 base for M1 scalping
    if(StringFind(symbol, "BTC") >= 0)
    {
        baseTarget = 5.0; // $5 base for BTC due to higher volatility
    }
    else if(StringFind(symbol, "XAU") >= 0 || StringFind(symbol, "GOLD") >= 0)
    {
        baseTarget = 4.0; // $4 base for XAU
    }

    // Apply Fibonacci progression to base target
    double rawTarget = baseTarget * g_profitSteps[stepIndex];

    // CRITICAL: Apply spread awareness to profit target
    double initialTarget = CalculateSpreadAwareProfitTarget(rawTarget);

    g_orders[orderIndex].initialTPTarget = initialTarget;
    g_orders[orderIndex].trailingProfitActive = false;
    g_orders[orderIndex].trailingTPLevel = 0;
    g_orders[orderIndex].maxProfitAchieved = currentProfit;
    g_orders[orderIndex].lockedProfit = 0;
    g_orders[orderIndex].lastTrailingUpdate = TimeCurrent();
    g_orders[orderIndex].trailingUpdateCount = 0;

    LogMessage(StringFormat("🎯 M1 SCALPING TARGET: Ticket %d, Base=$%.2f, Raw=$%.2f, Spread-Aware=$%.2f",
              g_orders[orderIndex].ticket, baseTarget, rawTarget, initialTarget));
}

//+------------------------------------------------------------------+
//| Enhanced trailing profit activation with profit cushion          |
//+------------------------------------------------------------------+
bool ShouldActivateTrailingProfit(int orderIndex, double currentProfit)
{
    double triggerLevel = g_orders[orderIndex].initialTPTarget * g_configuredTrailingProfitTrigger;
    double cushionLevel = g_orders[orderIndex].initialTPTarget * g_configuredProfitCushion;

    // Option 1: Activate only after reaching initial TP
    if(g_configuredTrailingProfitOnlyAfterTP)
    {
        return (currentProfit >= g_orders[orderIndex].initialTPTarget);
    }

    // Option 2: Enhanced activation with profit cushion
    // Ensure we have a minimum profit cushion before starting to trail
    bool hasMinimumProfit = (currentProfit >= cushionLevel);
    bool reachedTrigger = (currentProfit >= triggerLevel);

    return (hasMinimumProfit && reachedTrigger);
}

//+------------------------------------------------------------------+
//| Activate trailing profit for position                            |
//+------------------------------------------------------------------+
void ActivateTrailingProfit(int orderIndex, double currentProfit)
{
    g_orders[orderIndex].trailingProfitActive = true;
    g_orders[orderIndex].maxProfitAchieved = currentProfit;

    // Set initial locked profit (minimum profit to protect)
    g_orders[orderIndex].lockedProfit = currentProfit * g_configuredTrailingProfitMinLock;

    LogMessage(StringFormat("🚀 TRAILING PROFIT ACTIVATED: Ticket %d, Current: $%.2f, Locked: $%.2f",
              g_orders[orderIndex].ticket, currentProfit, g_orders[orderIndex].lockedProfit));
}

//+------------------------------------------------------------------+
//| Enhanced trailing TP calculation with dynamic adjustments        |
//+------------------------------------------------------------------+
double CalculateTrailingTPLevel(int orderIndex, double currentProfit)
{
    double maxProfit = g_orders[orderIndex].maxProfitAchieved;
    double additionalProfit = maxProfit - g_orders[orderIndex].initialTPTarget;

    // Calculate dynamic trailing step based on volatility
    double dynamicStep = g_configuredTrailingProfitStep;
    if(g_configuredUseDynamicTrailing)
    {
        double currentATR = CalculateATR();
        double avgATR = CalculateAverageATR();

        if(avgATR > 0)
        {
            double volatilityRatio = currentATR / avgATR;

            // Adjust trailing step based on volatility
            if(volatilityRatio > 1.5) // High volatility - more conservative trailing
            {
                dynamicStep = g_configuredTrailingProfitStep * 0.7; // Reduce step by 30%
            }
            else if(volatilityRatio < 0.7) // Low volatility - more aggressive trailing
            {
                dynamicStep = g_configuredTrailingProfitStep * 1.3; // Increase step by 30%
            }

            // Apply volatility multiplier
            dynamicStep *= g_configuredVolatilityMultiplier;
        }
    }

    // Trail by dynamic percentage of additional profit beyond initial TP
    double trailAmount = additionalProfit * dynamicStep;
    double newLockedProfit = maxProfit - trailAmount;

    // Enhanced minimum lock calculation with profit cushion
    double minLocked = maxProfit * g_configuredTrailingProfitMinLock;
    double cushionAdjustedMin = g_orders[orderIndex].initialTPTarget * g_configuredProfitCushion;
    minLocked = MathMax(minLocked, cushionAdjustedMin);

    newLockedProfit = MathMax(newLockedProfit, minLocked);

    // Ensure we don't reduce locked profit
    newLockedProfit = MathMax(newLockedProfit, g_orders[orderIndex].lockedProfit);

    // Log dynamic adjustments
    if(g_configuredUseDynamicTrailing)
    {
        double currentATR = CalculateATR();
        LogMessage(StringFormat("🔄 Dynamic Trailing: Step %.3f (base %.3f), Volatility Ratio: %.2f",
                  dynamicStep, g_configuredTrailingProfitStep, currentATR / MathMax(CalculateAverageATR(), 0.001)));
    }

    return newLockedProfit;
}

//+------------------------------------------------------------------+
//| Check if trailing TP should be updated                           |
//+------------------------------------------------------------------+
bool ShouldUpdateTrailingTP(int orderIndex, double newTPLevel, double currentTP, int positionType)
{
    // Only update if new level is significantly better
    double improvement = newTPLevel - g_orders[orderIndex].lockedProfit;

    // Require at least $1 improvement to avoid excessive modifications
    if(improvement < 1.0)
        return false;

    // Check if enough time has passed since last update
    datetime currentTime = TimeCurrent();
    if(currentTime - g_orders[orderIndex].lastTrailingUpdate < g_configuredTrailingProfitFrequency)
        return false;

    return true;
}

//+------------------------------------------------------------------+
//| Update trailing TP level                                         |
//+------------------------------------------------------------------+
void UpdateTrailingTP(int orderIndex, double newTPLevel, int positionType, double openPrice)
{
    // Convert profit amount to price level
    double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
    double pointValuePerLot = 0.10; // XAU/USD standard
    double pointValueForThisLot = pointValuePerLot * g_orders[orderIndex].lots;

    double requiredPoints = newTPLevel / pointValueForThisLot;
    double newTPPrice = 0;

    if(positionType == POSITION_TYPE_BUY)
    {
        newTPPrice = openPrice + (requiredPoints * point);
    }
    else
    {
        newTPPrice = openPrice - (requiredPoints * point);
    }

    // Normalize price
    int digits = (int)SymbolInfoInteger(Symbol(), SYMBOL_DIGITS);
    newTPPrice = NormalizeDouble(newTPPrice, digits);

    // Apply broker compliance check
    double currentPrice = (positionType == POSITION_TYPE_BUY) ?
                         SymbolInfoDouble(Symbol(), SYMBOL_BID) :
                         SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    double minStopLevel = SymbolInfoInteger(Symbol(), SYMBOL_TRADE_STOPS_LEVEL) * point;
    if(minStopLevel == 0) minStopLevel = 100 * point;

    bool isValidTP = false;
    if(positionType == POSITION_TYPE_BUY)
    {
        isValidTP = (newTPPrice > currentPrice + minStopLevel);
    }
    else
    {
        isValidTP = (newTPPrice < currentPrice - minStopLevel);
    }

    if(!isValidTP)
    {
        LogMessage(StringFormat("⚠️ Trailing TP rejected: Too close to market (Ticket: %d)",
                  g_orders[orderIndex].ticket));
        return;
    }

    // Attempt to modify position
    double currentSL = PositionGetDouble(POSITION_SL);
    if(Trade.PositionModify(g_orders[orderIndex].ticket, currentSL, newTPPrice))
    {
        // Update tracking data
        double oldLockedProfit = g_orders[orderIndex].lockedProfit;
        g_orders[orderIndex].lockedProfit = newTPLevel;
        g_orders[orderIndex].trailingTPLevel = newTPPrice;
        g_orders[orderIndex].lastTrailingUpdate = TimeCurrent();
        g_orders[orderIndex].trailingUpdateCount++;

        // Track total trailing profit gained
        double additionalProfit = newTPLevel - oldLockedProfit;
        g_totalTrailingProfit += additionalProfit;
        g_trailingProfitCount++;

        LogMessage(StringFormat("📈 TRAILING TP UPDATED: Ticket %d, New TP: %.3f, Locked: $%.2f (+$%.2f)",
                  g_orders[orderIndex].ticket, newTPPrice, newTPLevel, additionalProfit));
    }
    else
    {
        int error = GetLastError();
        LogMessage(StringFormat("❌ Trailing TP Failed: Ticket %d, Error: %d",
                  g_orders[orderIndex].ticket, error));
    }
}

//+------------------------------------------------------------------+
//| Initialize all technical indicators                              |
//+------------------------------------------------------------------+
bool InitializeIndicators()
{
    // Moving Averages
    g_maFastHandle = iMA(Symbol(), PERIOD_M1, MA_Fast_Period, 0, MODE_EMA, PRICE_CLOSE);
    g_maSlowHandle = iMA(Symbol(), PERIOD_M1, MA_Slow_Period, 0, MODE_EMA, PRICE_CLOSE);

    // RSI
    g_rsiHandle = iRSI(Symbol(), PERIOD_M1, RSI_Period, PRICE_CLOSE);

    // ADX
    g_adxHandle = iADX(Symbol(), PERIOD_M1, ADX_Period);

    // MACD
    g_macdHandle = iMACD(Symbol(), PERIOD_M1, MACD_Fast, MACD_Slow, MACD_Signal, PRICE_CLOSE);

    // Stochastic
    g_stochHandle = iStochastic(Symbol(), PERIOD_M1, Stoch_K, Stoch_D, Stoch_Slowing, MODE_SMA, STO_LOWHIGH);

    // Bollinger Bands
    g_bbHandle = iBands(Symbol(), PERIOD_M1, 20, 0, 2.0, PRICE_CLOSE);

    // Verify all handles are valid
    if(g_maFastHandle == INVALID_HANDLE || g_maSlowHandle == INVALID_HANDLE ||
       g_rsiHandle == INVALID_HANDLE || g_adxHandle == INVALID_HANDLE ||
       g_macdHandle == INVALID_HANDLE || g_stochHandle == INVALID_HANDLE ||
       g_bbHandle == INVALID_HANDLE)
    {
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Enhanced market signal with confluence analysis                  |
//+------------------------------------------------------------------+
int GetMarketSignal(double &signalStrength)
{
    // Step 0: Detect current market regime
    g_currentMarketRegime = DetectMarketRegime();
    g_lastRegimeUpdate = TimeCurrent();

    // Step 0.1: Check if we should avoid trading in current conditions
    if(ShouldAvoidTrading(g_currentMarketRegime))
    {
        // Still return a signal (never SIGNAL_NONE) but with very low strength
        signalStrength = 0.10; // Very low strength to discourage trading
        g_currentSignalStrength = signalStrength;
        LogMessage("🚫 TRADE AVOIDED: Market conditions not suitable, returning weak signal");
        return ORDER_TYPE_BUY; // Default direction
    }

    // Step 1: Get base enhanced signal (NEVER returns SIGNAL_NONE)
    double baseStrength = 0.0;

    // Calculate enhanced bull and bear scores
    double bullScore = CalculateEnhancedBullScore();
    double bearScore = CalculateEnhancedBearScore();

    // Add trend momentum boost
    double trendBoost = GetTrendMomentumBoost();

    // Add volume confirmation boost
    double volumeBoost = GetVolumeConfirmationBoost();

    // Determine base signal direction and strength
    ENUM_SIGNAL_TYPE baseSignal;
    if(bullScore > bearScore)
    {
        baseStrength = bullScore + trendBoost + volumeBoost;
        baseSignal = SIGNAL_BUY;
    }
    else
    {
        baseStrength = bearScore + trendBoost + volumeBoost;
        baseSignal = SIGNAL_SELL;
    }

    baseStrength = MathMin(baseStrength, 1.0); // Cap at 1.0

    // Step 2: Add multi-timeframe confluence
    double confluenceBoost = CalculateTimeframeConfluence(baseSignal);
    g_confluenceBoost = confluenceBoost;

    // Step 3: Add market condition boost
    double marketBoost = CalculateMarketConditionBoost();
    g_marketConditionBoost = marketBoost;

    // Step 4: Add volatility boost
    double volatilityBoost = 0.0;
    double currentATR = CalculateATR();
    double avgATR = CalculateAverageATR();
    if(avgATR > 0)
    {
        double volatilityRatio = currentATR / avgATR;
        if(volatilityRatio > 1.3) volatilityBoost = 0.10;
        else if(volatilityRatio > 1.1) volatilityBoost = 0.05;
    }
    g_volatilityBoost = volatilityBoost;

    // Step 5: Calculate final enhanced strength
    double finalStrength = baseStrength + confluenceBoost + marketBoost + volatilityBoost;
    finalStrength = MathMin(finalStrength, 1.0); // Cap at 1.0

    // Step 6: Apply regime-based signal validation
    double minimumThreshold = GetMinimumSignalThreshold(g_currentMarketRegime);
    int requiredConfluence = GetRequiredConfluenceSignals(g_currentMarketRegime);

    // Check if signal meets regime requirements
    bool meetsThreshold = finalStrength >= minimumThreshold;
    bool meetsConfluence = (g_m5TrendAlignment ? 1 : 0) +
                          (g_m15TrendAlignment ? 1 : 0) +
                          (g_h1TrendAlignment ? 1 : 0) >= requiredConfluence;

    // Apply regime-based adjustments
    if(!meetsThreshold || !meetsConfluence)
    {
        // Reduce signal strength for regime non-compliance
        finalStrength *= 0.5;
        LogMessage(StringFormat("⚠️ REGIME FILTER: Signal reduced due to threshold (%.3f < %.3f) or confluence (%s)",
                   finalStrength * 2, minimumThreshold, meetsConfluence ? "OK" : "FAIL"));
    }

    // Step 7: Update global variables for other functions
    g_currentSignalStrength = finalStrength;
    g_finalEnhancedStrength = finalStrength;
    signalStrength = finalStrength;

    // Step 8: Add to signal strength sum for performance tracking
    g_signalStrengthSum += finalStrength;

    // Step 9: Log comprehensive enhancement details with regime info
    string regimeName = "";
    switch(g_currentMarketRegime)
    {
        case MARKET_TRENDING_UP: regimeName = "TRENDING UP"; break;
        case MARKET_TRENDING_DOWN: regimeName = "TRENDING DOWN"; break;
        case MARKET_RANGING: regimeName = "RANGING"; break;
        case MARKET_BREAKOUT: regimeName = "BREAKOUT"; break;
        case MARKET_UNCERTAIN: regimeName = "UNCERTAIN"; break;
        default: regimeName = "UNKNOWN"; break;
    }

    LogMessage(StringFormat("📊 REGIME ANALYSIS: %s (Confidence: %.2f), Threshold: %.3f, Confluence: %d/%d",
               regimeName, g_regimeConfidence, minimumThreshold,
               (g_m5TrendAlignment ? 1 : 0) + (g_m15TrendAlignment ? 1 : 0) + (g_h1TrendAlignment ? 1 : 0), 3));

    LogMessage(StringFormat("📊 SIGNAL ENHANCEMENT: Base=%.3f, Confluence=+%.3f, Market=+%.3f, Volatility=+%.3f, Final=%.3f",
               baseStrength, confluenceBoost, marketBoost, volatilityBoost, finalStrength));

    // Step 10: Log signal quality assessment
    string qualityLevel = "";
    if(finalStrength >= 0.40) qualityLevel = "🎯 HIGH QUALITY";
    else if(finalStrength >= 0.25) qualityLevel = "✅ STANDARD QUALITY";
    else if(finalStrength >= 0.15) qualityLevel = "⚠️ LOW QUALITY";
    else qualityLevel = "🔻 VERY LOW QUALITY";

    LogMessage(StringFormat("%s SIGNAL: %s (Strength: %.3f, Regime: %s)",
               qualityLevel, (baseSignal == SIGNAL_BUY) ? "BUY" : "SELL", finalStrength, regimeName));

    // Step 11: Return signal direction (ALWAYS BUY or SELL, never SIGNAL_NONE)
    return (baseSignal == SIGNAL_BUY) ? ORDER_TYPE_BUY : ORDER_TYPE_SELL;
}

//+------------------------------------------------------------------+
//| Individual indicator signal functions                            |
//+------------------------------------------------------------------+
double GetMASignal()
{
    if(g_maFastHandle == INVALID_HANDLE || g_maSlowHandle == INVALID_HANDLE)
        return 0.0;

    double maFast[], maSlow[];
    ArraySetAsSeries(maFast, true);
    ArraySetAsSeries(maSlow, true);

    int fastCopied = CopyBuffer(g_maFastHandle, 0, 0, 2, maFast);
    int slowCopied = CopyBuffer(g_maSlowHandle, 0, 0, 2, maSlow);

    if(fastCopied < 2 || slowCopied < 2)
    {
        LogMessage(StringFormat("MA Signal Error: Fast copied %d, Slow copied %d", fastCopied, slowCopied));
        return 0.0;
    }

    // Current and previous values
    double fastCurrent = maFast[0], fastPrev = maFast[1];
    double slowCurrent = maSlow[0], slowPrev = maSlow[1];

    // Validate data
    if(fastCurrent <= 0 || slowCurrent <= 0 || fastPrev <= 0 || slowPrev <= 0)
        return 0.0;

    // Signal strength based on crossover and distance
    if(fastCurrent > slowCurrent)
    {
        double strength = MathMin((fastCurrent - slowCurrent) / slowCurrent * 100, 1.0);
        return (fastCurrent > fastPrev) ? strength : strength * 0.5;
    }
    else
    {
        double strength = MathMin((slowCurrent - fastCurrent) / slowCurrent * 100, 1.0);
        return (fastCurrent < fastPrev) ? -strength : -strength * 0.5;
    }
}

double GetRSISignal()
{
    if(g_rsiHandle == INVALID_HANDLE)
        return 0.0;

    double rsi[];
    ArraySetAsSeries(rsi, true);

    int copied = CopyBuffer(g_rsiHandle, 0, 0, 1, rsi);
    if(copied < 1)
    {
        LogMessage(StringFormat("RSI Signal Error: Copied %d values", copied));
        return 0.0;
    }

    double rsiValue = rsi[0];

    // Validate RSI value
    if(rsiValue < 0 || rsiValue > 100)
        return 0.0;

    // RSI signal interpretation
    if(rsiValue > 70) return -0.8;      // Overbought - sell signal
    if(rsiValue < 30) return 0.8;       // Oversold - buy signal
    if(rsiValue > 50) return 0.3;       // Above midline - weak buy
    return -0.3;                        // Below midline - weak sell
}

double GetADXSignal()
{
    double adx[], plusDI[], minusDI[];
    ArraySetAsSeries(adx, true);
    ArraySetAsSeries(plusDI, true);
    ArraySetAsSeries(minusDI, true);

    if(CopyBuffer(g_adxHandle, 0, 0, 1, adx) < 1 ||
       CopyBuffer(g_adxHandle, 1, 0, 1, plusDI) < 1 ||
       CopyBuffer(g_adxHandle, 2, 0, 1, minusDI) < 1)
        return 0.0;

    double adxValue = adx[0];
    double plusValue = plusDI[0];
    double minusValue = minusDI[0];

    // Strong trend confirmation
    if(adxValue > 25)
    {
        double strength = MathMin(adxValue / 50.0, 1.0);
        return (plusValue > minusValue) ? strength : -strength;
    }

    return 0.0; // Weak trend
}

//+------------------------------------------------------------------+
//| New momentum signal function                                     |
//+------------------------------------------------------------------+
double GetMomentumSignal()
{
    double close[], high[], low[];
    ArraySetAsSeries(close, true);
    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);

    if(CopyClose(Symbol(), PERIOD_M1, 0, 10, close) < 10 ||
       CopyHigh(Symbol(), PERIOD_M1, 0, 10, high) < 10 ||
       CopyLow(Symbol(), PERIOD_M1, 0, 10, low) < 10)
        return 0.0;

    // Calculate momentum indicators
    double priceChange = (close[0] - close[5]) / close[5] * 100; // 5-period price change
    double volatility = (high[0] - low[0]) / close[0] * 100;     // Current volatility

    // Momentum signal based on price change and volatility
    if(MathAbs(priceChange) > 0.1 && volatility < 0.5) // Strong momentum, low volatility
    {
        return (priceChange > 0) ? 0.7 : -0.7;
    }
    else if(MathAbs(priceChange) > 0.05) // Moderate momentum
    {
        return (priceChange > 0) ? 0.4 : -0.4;
    }

    return 0.0; // No clear momentum
}

//+------------------------------------------------------------------+
//| Get ADX value for trend confirmation                             |
//+------------------------------------------------------------------+
double GetADXValue()
{
    double adx[];
    ArraySetAsSeries(adx, true);

    if(CopyBuffer(g_adxHandle, 0, 0, 1, adx) < 1)
        return 0.0;

    return adx[0];
}

//+------------------------------------------------------------------+
//| Analyze signal confluence                                        |
//+------------------------------------------------------------------+
void AnalyzeSignalConfluence(SignalConfluence &confluence, double ma, double rsi, double adx, double macd, double stoch, double bb, double momentum)
{
    confluence.bullish_signals = 0;
    confluence.bearish_signals = 0;
    confluence.neutral_signals = 0;
    confluence.total_strength = 0.0;

    // Count bullish/bearish signals
    if(ma > 0.3) confluence.bullish_signals++; else if(ma < -0.3) confluence.bearish_signals++; else confluence.neutral_signals++;
    if(rsi > 0.3) confluence.bullish_signals++; else if(rsi < -0.3) confluence.bearish_signals++; else confluence.neutral_signals++;
    if(adx > 0.3) confluence.bullish_signals++; else if(adx < -0.3) confluence.bearish_signals++; else confluence.neutral_signals++;
    if(macd > 0.3) confluence.bullish_signals++; else if(macd < -0.3) confluence.bearish_signals++; else confluence.neutral_signals++;
    if(stoch > 0.3) confluence.bullish_signals++; else if(stoch < -0.3) confluence.bearish_signals++; else confluence.neutral_signals++;
    if(bb > 0.3) confluence.bullish_signals++; else if(bb < -0.3) confluence.bearish_signals++; else confluence.neutral_signals++;
    if(momentum > 0.3) confluence.bullish_signals++; else if(momentum < -0.3) confluence.bearish_signals++; else confluence.neutral_signals++;

    // Calculate trend strength
    double totalSignals = confluence.bullish_signals + confluence.bearish_signals + confluence.neutral_signals;
    if(totalSignals > 0)
    {
        double dominantSignals = MathMax(confluence.bullish_signals, confluence.bearish_signals);
        confluence.trend_strength = dominantSignals / totalSignals;
    }

    // Calculate total strength
    confluence.total_strength = (MathAbs(ma) + MathAbs(rsi) + MathAbs(adx) + MathAbs(macd) + MathAbs(stoch) + MathAbs(bb) + MathAbs(momentum)) / 7.0;

    // Determine if trending (using configured parameters)
    confluence.is_trending = (confluence.trend_strength >= g_configuredMinTrendStrength && GetADXValue() >= g_configuredADXTrendThreshold);
}

//+------------------------------------------------------------------+
//| Calculate enhanced signal strength                               |
//+------------------------------------------------------------------+
double CalculateEnhancedSignalStrength(SignalConfluence &confluence, double totalScore)
{
    double baseStrength = MathMin(MathAbs(totalScore), 1.0);

    // Boost strength for confluence (using configured parameters)
    double confluenceBoost = 1.0;
    if(confluence.bullish_signals >= g_configuredMinConfluenceSignals || confluence.bearish_signals >= g_configuredMinConfluenceSignals)
    {
        confluenceBoost = 1.0 + (confluence.trend_strength * 0.5); // Up to 50% boost
    }

    // Boost for trending markets
    double trendBoost = 1.0;
    if(confluence.is_trending)
    {
        trendBoost = 1.2; // 20% boost for trending markets
    }

    double enhancedStrength = baseStrength * confluenceBoost * trendBoost;
    return MathMin(enhancedStrength, 1.0); // Cap at 1.0
}

double GetMACDSignal()
{
    double macd[], signal[];
    ArraySetAsSeries(macd, true);
    ArraySetAsSeries(signal, true);

    if(CopyBuffer(g_macdHandle, 0, 0, 2, macd) < 2 ||
       CopyBuffer(g_macdHandle, 1, 0, 2, signal) < 2)
        return 0.0;

    double macdCurrent = macd[0], macdPrev = macd[1];
    double signalCurrent = signal[0], signalPrev = signal[1];

    // MACD crossover signals
    if(macdCurrent > signalCurrent && macdPrev <= signalPrev)
        return 0.7; // Bullish crossover
    if(macdCurrent < signalCurrent && macdPrev >= signalPrev)
        return -0.7; // Bearish crossover

    // MACD position relative to signal line
    return (macdCurrent > signalCurrent) ? 0.3 : -0.3;
}

double GetStochasticSignal()
{
    double stochMain[], stochSignal[];
    ArraySetAsSeries(stochMain, true);
    ArraySetAsSeries(stochSignal, true);

    if(CopyBuffer(g_stochHandle, 0, 0, 1, stochMain) < 1 ||
       CopyBuffer(g_stochHandle, 1, 0, 1, stochSignal) < 1)
        return 0.0;

    double mainValue = stochMain[0];
    double signalValue = stochSignal[0];

    // Stochastic signal interpretation
    if(mainValue > 80) return -0.6;     // Overbought
    if(mainValue < 20) return 0.6;      // Oversold
    return (mainValue > signalValue) ? 0.2 : -0.2;
}

double GetBollingerSignal()
{
    double bbUpper[], bbMiddle[], bbLower[];
    ArraySetAsSeries(bbUpper, true);
    ArraySetAsSeries(bbMiddle, true);
    ArraySetAsSeries(bbLower, true);

    if(CopyBuffer(g_bbHandle, 1, 0, 1, bbUpper) < 1 ||
       CopyBuffer(g_bbHandle, 0, 0, 1, bbMiddle) < 1 ||
       CopyBuffer(g_bbHandle, 2, 0, 1, bbLower) < 1)
        return 0.0;

    double currentPrice = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double upperBand = bbUpper[0];
    double lowerBand = bbLower[0];
    double middleBand = bbMiddle[0];

    // Bollinger Bands signal
    if(currentPrice > upperBand) return -0.5;  // Price above upper band - sell
    if(currentPrice < lowerBand) return 0.5;   // Price below lower band - buy
    return (currentPrice > middleBand) ? 0.1 : -0.1;
}

//+------------------------------------------------------------------+
//| Execute trade signal with enhanced risk management               |
//+------------------------------------------------------------------+
void ExecuteTradeSignal(int signal, double signalStrength)
{
    // Validate trading conditions
    if(!TerminalInfoInteger(TERMINAL_TRADE_ALLOWED))
    {
        LogMessage("ERROR: Trading not allowed in terminal");
        return;
    }

    if(!MQLInfoInteger(MQL_TRADE_ALLOWED))
    {
        LogMessage("ERROR: EA trading not allowed");
        return;
    }

    // Get next available chain for parallel order management
    int chainId = GetNextAvailableChain();
    if(chainId == -1)
    {
        LogMessage("All 3 order chains are active - waiting for available slot");
        return;
    }

    double lotSize = CalculateLotSize(signalStrength, chainId);
    if(lotSize <= 0)
    {
        LogMessage("ERROR: Invalid lot size calculated");
        return;
    }

    double price = (signal == ORDER_TYPE_BUY) ? SymbolInfoDouble(Symbol(), SYMBOL_ASK) : SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double sl = 0, tp = 0;

    // Calculate stop loss and take profit - use asymmetric system if enabled
    if(g_useAsymmetricRR && g_asymmetricRisk.asymmetricSystemEnabled)
    {
        CalculateAsymmetricStopLevels(signal, price, sl, tp, lotSize, chainId);

        // Check if asymmetric calculation failed
        if(sl == 0 && tp == 0)
        {
            LogMessage("TRADE EXECUTION FAILED: Asymmetric risk validation rejected trade");
            return;
        }
    }
    else
    {
        // Use original calculation method
        CalculateStopLevels(signal, price, sl, tp, lotSize, chainId);
    }

    string comment = StringFormat("EA_Order_%d", g_orderCount + 1);
    ulong ticket = 0;

    LogMessage(StringFormat("Attempting to open %s order: %.2f lots at %.5f",
              (signal == ORDER_TYPE_BUY) ? "BUY" : "SELL", lotSize, price));

    // Execute trade
    if(signal == ORDER_TYPE_BUY)
    {
        if(Trade.Buy(lotSize, Symbol(), price, sl, tp, comment))
            ticket = Trade.ResultOrder();
    }
    else
    {
        if(Trade.Sell(lotSize, Symbol(), price, sl, tp, comment))
            ticket = Trade.ResultOrder();
    }

    // Track the order
    if(ticket > 0)
    {
        AddOrderToTracking(ticket, signal, lotSize, price, tp, sl, comment, signalStrength, chainId);
        ActivateOrderChain(chainId, ticket);
        LogMessage(StringFormat("✅ ORDER OPENED: %s %.2f lots at %.3f [Ticket: %d, Chain: %d]",
                  (signal == ORDER_TYPE_BUY) ? "BUY" : "SELL", lotSize, price, ticket, chainId));
        LogMessage(StringFormat("📊 INITIAL SL/TP: SL=%.3f, TP=%.3f (Signal: %.2f)",
                  sl, tp, signalStrength));
        LogMessage(StringFormat("🔗 CHAIN %d: Step %d, Target: $15.00",
                  chainId, g_orderChains[chainId-1].currentStep + 1));
        g_lastTradeTime = TimeCurrent();
    }
    else
    {
        int error = GetLastError();
        LogMessage(StringFormat("❌ ORDER FAILED: %s %.2f lots - Error: %d (%s)",
                  (signal == ORDER_TYPE_BUY) ? "BUY" : "SELL", lotSize, error, ErrorDescription(error)));

        // Reset error
        ResetLastError();
    }
}

//+------------------------------------------------------------------+
//| Calculate optimal lot size using Enhanced Anti-Martingale       |
//+------------------------------------------------------------------+
double CalculateLotSize(double signalStrength, int chainId = -1)
{
    double baseLot = InitialLot;
    double antiMartingaleLot = baseLot;

    // === ENHANCED ANTI-MARTINGALE CORE LOGIC FOR PARALLEL ORDERS ===
    // Use individual chain state if specified, otherwise use global state

    if(chainId > 0 && chainId <= 3)
    {
        // Use specific chain's Anti-Martingale state
        int chainIndex = chainId - 1;
        OrderChain chain = g_orderChains[chainIndex];

        if(chain.consecutiveWins > 0 && chain.isInWinningStreak)
        {
            // Progressive scaling based on this chain's win streak
            int winIndex = MathMin(chain.consecutiveWins - 1, ArraySize(g_winSequence) - 1);
            winIndex = MathMin(winIndex, g_configuredMaxAntiMartingaleStep - 1);
            double winMultiplier = g_winSequence[winIndex];

            antiMartingaleLot = baseLot * winMultiplier;
            LogMessage(StringFormat("Chain %d Anti-Martingale: %d wins, %.3f lot (%.2fx)",
                      chainId, chain.consecutiveWins, antiMartingaleLot, winMultiplier));
        }
        else
        {
            antiMartingaleLot = baseLot;
            LogMessage(StringFormat("Chain %d Anti-Martingale: Reset to base %.3f lot", chainId, baseLot));
        }
    }
    else
    {
        // Fallback to global Anti-Martingale state (legacy compatibility)
        if(g_consecutiveWins > 0 && g_isInWinningStreak)
        {
            int winIndex = MathMin(g_consecutiveWins - 1, ArraySize(g_winSequence) - 1);
            winIndex = MathMin(winIndex, g_configuredMaxAntiMartingaleStep - 1);
            double winMultiplier = g_winSequence[winIndex];

            antiMartingaleLot = baseLot * winMultiplier;
            LogMessage(StringFormat("Global Anti-Martingale: %d wins, %.3f lot (%.2fx)",
                      g_consecutiveWins, antiMartingaleLot, winMultiplier));
        }
        else
        {
            antiMartingaleLot = baseLot;
            LogMessage(StringFormat("Global Anti-Martingale: Reset to base %.3f lot", baseLot));
        }
    }

    // Start with Anti-Martingale lot as base for further adjustments
    double finalLot = antiMartingaleLot;

    // === CONSERVATIVE SIGNAL STRENGTH ADJUSTMENT ===
    // Only apply minor adjustments to preserve Anti-Martingale progression
    double signalMultiplier = 0.95 + (signalStrength * 0.1); // Range: 0.95 to 1.05 (CONSERVATIVE)

    // MOMENTUM ACCELERATION: Only for very strong signals and limited impact (using configured parameters)
    if(UseMomentumAcceleration && signalStrength >= g_configuredMomentumThreshold)
    {
        double momentumBoost = 1.0 + ((signalStrength - g_configuredMomentumThreshold) * 0.2); // Max 10% boost
        finalLot *= momentumBoost;
        LogMessage(StringFormat("Momentum boost: %.2f → %.3f lot", signalStrength, finalLot));
    }
    else if(g_consecutiveWins == 0) // Only apply to first trade to avoid interfering with progression
    {
        finalLot *= signalMultiplier;
    }

    // PHASE 2: Win streak momentum multiplier (+10% per consecutive win after 3rd, capped at 50%)
    double momentumMultiplier = CalculateWinStreakMomentum(chainId);
    finalLot *= momentumMultiplier;

    // === REGIME-BASED DYNAMIC RISK MANAGEMENT ===
    if(g_currentSignalStrength > 0)
    {
        // Calculate regime-based lot size (includes quality adjustment)
        double regimeAdjustedLot = CalculateRegimeBasedLotSize(finalLot, g_currentMarketRegime, g_currentSignalStrength);

        LogMessage(StringFormat("🎯 REGIME LOT SIZING: Original=%.3f, Regime-Adjusted=%.3f (Signal: %.3f, Regime: %s)",
                   finalLot, regimeAdjustedLot, g_currentSignalStrength,
                   g_currentMarketRegime == MARKET_RANGING ? "RANGING" :
                   g_currentMarketRegime == MARKET_TRENDING_UP ? "TRENDING_UP" :
                   g_currentMarketRegime == MARKET_TRENDING_DOWN ? "TRENDING_DOWN" :
                   g_currentMarketRegime == MARKET_BREAKOUT ? "BREAKOUT" : "UNCERTAIN"));

        finalLot = regimeAdjustedLot;
        g_qualityAdjustedLot = finalLot;
    }

    // === CONSERVATIVE VOLATILITY ADJUSTMENT ===
    // Minor adjustments only to preserve Anti-Martingale progression
    double atr = CalculateATR();
    double avgATR = CalculateAverageATR();

    if(avgATR > 0 && g_consecutiveWins == 0) // Only apply to first trade
    {
        double volatilityRatio = atr / avgATR;
        if(volatilityRatio > 2.0) // Extreme volatility only
        {
            finalLot *= 0.9; // Minor reduction by 10%
            LogMessage(StringFormat("High volatility: %.2f → %.3f lot", volatilityRatio, finalLot));
        }
        else if(volatilityRatio < 0.5) // Very low volatility
        {
            finalLot *= 1.1; // Minor increase by 10%
            LogMessage(StringFormat("Low volatility: %.2f → %.3f lot", volatilityRatio, finalLot));
        }
    }

    // === RISK MANAGEMENT LIMITS ===
    double beforeRiskManagement = finalLot;

    // Maximum 5% of free margin per trade
    double freeMargin = AccountInfoDouble(ACCOUNT_FREEMARGIN);
    double maxLot = freeMargin * 0.05 / 1000;
    if(finalLot > maxLot)
    {
        LogMessage(StringFormat("⚠️ RISK LIMIT: Lot reduced from %.3f to %.3f (Max=%.3f, FreeMargin=$%.2f)",
                  finalLot, maxLot, maxLot, freeMargin));
        finalLot = maxLot;
    }

    // Normalize lot size to broker requirements
    double minLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
    double maxAllowedLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);
    double lotStep = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_STEP);

    LogMessage(StringFormat("📋 BROKER LIMITS: Min=%.3f, Max=%.3f, Step=%.3f",
              minLot, maxAllowedLot, lotStep));

    finalLot = MathMax(finalLot, minLot);
    finalLot = MathMin(finalLot, maxAllowedLot);

    // Normalize to lot step
    double beforeNormalization = finalLot;
    finalLot = NormalizeDouble(finalLot / lotStep, 0) * lotStep;

    LogMessage(StringFormat("Final lot: %.3f (Step %d)", finalLot, g_currentStep + 1));

    // Store final lot for monitoring
    g_qualityAdjustedLot = finalLot;

    LogMessage(StringFormat("📊 FINAL LOT CALCULATION: %.3f (Chain: %d, Signal Strength: %.3f)",
               finalLot, chainId, g_currentSignalStrength));

    return finalLot;
}

//+------------------------------------------------------------------+
//| PHASE 1: Calculate adaptive interval based on ATR volatility    |
//+------------------------------------------------------------------+
int CalculateAdaptiveInterval()
{
    // Get current ATR (14-period)
    double currentATR = CalculateATR();

    // Calculate average ATR over last 50 periods for comparison
    double avgATR = CalculateAverageATR();

    if(avgATR <= 0) return 30; // Fallback to default

    // Calculate volatility ratio
    double volatilityRatio = currentATR / avgATR;

    // PHASE 1: Adaptive intervals (15-45 seconds based on volatility)
    int baseInterval = 30;  // Base 30-second interval
    int adaptiveInterval = baseInterval;

    if(volatilityRatio > 1.5)
    {
        // High volatility: Faster intervals (15-20 seconds)
        adaptiveInterval = 15;
    }
    else if(volatilityRatio > 1.2)
    {
        // Medium-high volatility: Slightly faster (20-25 seconds)
        adaptiveInterval = 20;
    }
    else if(volatilityRatio < 0.7)
    {
        // Low volatility: Slower intervals (40-45 seconds)
        adaptiveInterval = 45;
    }
    else if(volatilityRatio < 0.8)
    {
        // Medium-low volatility: Slightly slower (35-40 seconds)
        adaptiveInterval = 35;
    }
    // Normal volatility (0.8-1.2): Keep base 30 seconds

    return adaptiveInterval;
}

//+------------------------------------------------------------------+
//| Enhanced Signal Calculation Functions                           |
//+------------------------------------------------------------------+
double CalculateEnhancedBullScore()
{
    double score = 0.0;

    // Get current signal values from individual indicator functions
    double maSignal = GetMASignal();
    double rsiSignal = GetRSISignal();
    double adxSignal = GetADXSignal();
    double macdSignal = GetMACDSignal();
    double stochSignal = GetStochasticSignal();
    double bbSignal = GetBollingerSignal();
    double momentumSignal = GetMomentumSignal();

    // Use existing signal calculations but with new weights
    if(maSignal > 0) score += g_weights.ma_weight * 0.5;
    if(rsiSignal > 0) score += g_weights.rsi_weight * 0.5;
    if(adxSignal > 0) score += g_weights.adx_weight * 0.5;
    if(macdSignal > 0) score += g_weights.macd_weight * 0.5;
    if(stochSignal > 0) score += g_weights.stoch_weight * 0.5;
    if(bbSignal > 0) score += g_weights.bb_weight * 0.5;
    if(momentumSignal > 0) score += g_weights.momentum_weight * 0.5;

    return score;
}

//+------------------------------------------------------------------+
//| Calculate enhanced bearish score                                |
//+------------------------------------------------------------------+
double CalculateEnhancedBearScore()
{
    double score = 0.0;

    // Get current signal values from individual indicator functions
    double maSignal = GetMASignal();
    double rsiSignal = GetRSISignal();
    double adxSignal = GetADXSignal();
    double macdSignal = GetMACDSignal();
    double stochSignal = GetStochasticSignal();
    double bbSignal = GetBollingerSignal();
    double momentumSignal = GetMomentumSignal();

    if(maSignal < 0) score += g_weights.ma_weight * 0.5;
    if(rsiSignal < 0) score += g_weights.rsi_weight * 0.5;
    if(adxSignal < 0) score += g_weights.adx_weight * 0.5;
    if(macdSignal < 0) score += g_weights.macd_weight * 0.5;
    if(stochSignal < 0) score += g_weights.stoch_weight * 0.5;
    if(bbSignal < 0) score += g_weights.bb_weight * 0.5;
    if(momentumSignal < 0) score += g_weights.momentum_weight * 0.5;

    return score;
}

//+------------------------------------------------------------------+
//| Get trend momentum boost                                        |
//+------------------------------------------------------------------+
double GetTrendMomentumBoost()
{
    // Use existing ADX handle if available, otherwise calculate directly
    if(g_adxHandle != INVALID_HANDLE)
    {
        double adx[];
        ArraySetAsSeries(adx, true);
        if(CopyBuffer(g_adxHandle, 0, 0, 1, adx) >= 1)
        {
            double adxValue = adx[0];
            return (adxValue > 25) ? 0.15 : 0.05;
        }
    }

    // Fallback: Use GetADXValue() function
    double adxValue = GetADXValue();
    return (adxValue > 25) ? 0.15 : 0.05;
}

//+------------------------------------------------------------------+
//| Get volume confirmation boost                                   |
//+------------------------------------------------------------------+
double GetVolumeConfirmationBoost()
{
    long currentVolume = iVolume(_Symbol, PERIOD_M1, 0);
    long avgVolume = 0;
    for(int i = 1; i <= 20; i++)
    {
        avgVolume += iVolume(_Symbol, PERIOD_M1, i);
    }
    avgVolume /= 20;

    return (currentVolume > avgVolume * 1.2) ? 0.10 : 0.0;
}

//+------------------------------------------------------------------+
//| PHASE 2: Calculate volatility-adjusted SL amount               |
//+------------------------------------------------------------------+
double CalculateVolatilityAdjustedSL(double baseSLAmount)
{
    // Get current ATR (14-period)
    double currentATR = CalculateATR();

    // Calculate average ATR over last 50 periods for comparison
    double avgATR = CalculateAverageATR();

    if(avgATR <= 0) return baseSLAmount; // Fallback to base SL

    // Calculate volatility ratio
    double volatilityRatio = currentATR / avgATR;

    // PHASE 2: Volatility adjustment (±20% range)
    double adjustmentFactor = 1.0;

    if(volatilityRatio > 1.3)
    {
        // High volatility: Wider SL (+20%)
        adjustmentFactor = 1.2;
    }
    else if(volatilityRatio > 1.1)
    {
        // Medium-high volatility: Slightly wider SL (+10%)
        adjustmentFactor = 1.1;
    }
    else if(volatilityRatio < 0.7)
    {
        // Low volatility: Tighter SL (-20%)
        adjustmentFactor = 0.8;
    }
    else if(volatilityRatio < 0.9)
    {
        // Medium-low volatility: Slightly tighter SL (-10%)
        adjustmentFactor = 0.9;
    }
    // Normal volatility (0.9-1.1): Keep base SL (factor = 1.0)

    double adjustedSL = baseSLAmount * adjustmentFactor;

    return adjustedSL;
}

//+------------------------------------------------------------------+
//| Multi-Timeframe Analysis Functions                              |
//+------------------------------------------------------------------+
ENUM_SIGNAL_TYPE GetTrendDirection(ENUM_TIMEFRAMES timeframe)
{
    // Use moving average crossover for trend detection
    double ma_fast = iMA(_Symbol, timeframe, 10, 0, MODE_EMA, PRICE_CLOSE);
    double ma_slow = iMA(_Symbol, timeframe, 20, 0, MODE_EMA, PRICE_CLOSE);

    if(ma_fast > ma_slow)
        return SIGNAL_BUY;
    else
        return SIGNAL_SELL;
}

//+------------------------------------------------------------------+
//| Calculate timeframe confluence boost                            |
//+------------------------------------------------------------------+
double CalculateTimeframeConfluence(ENUM_SIGNAL_TYPE baseSignal)
{
    double confluenceScore = 0.0;

    // M5 trend confirmation
    ENUM_SIGNAL_TYPE m5Trend = GetTrendDirection(PERIOD_M5);
    if(baseSignal == m5Trend)
    {
        confluenceScore += 0.20;
        LogMessage("✅ M5 Confluence: +0.20");
        g_m5TrendAlignment = true;
    }
    else
    {
        g_m5TrendAlignment = false;
    }

    // M15 trend confirmation
    ENUM_SIGNAL_TYPE m15Trend = GetTrendDirection(PERIOD_M15);
    if(baseSignal == m15Trend)
    {
        confluenceScore += 0.15;
        LogMessage("✅ M15 Confluence: +0.15");
        g_m15TrendAlignment = true;
    }
    else
    {
        g_m15TrendAlignment = false;
    }

    // H1 major trend confirmation
    ENUM_SIGNAL_TYPE h1Trend = GetTrendDirection(PERIOD_H1);
    if(baseSignal == h1Trend)
    {
        confluenceScore += 0.25;
        LogMessage("🎯 H1 Major Trend Confluence: +0.25");
        g_h1TrendAlignment = true;
    }
    else
    {
        g_h1TrendAlignment = false;
    }

    g_timeframeConfluenceScore = confluenceScore;
    return confluenceScore;
}

//+------------------------------------------------------------------+
//| Calculate market momentum                                       |
//+------------------------------------------------------------------+
double CalculateMarketMomentum()
{
    double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double price5ago = iClose(_Symbol, PERIOD_M1, 5);

    if(price5ago <= 0) return 0.0;

    return (currentPrice - price5ago) / price5ago;
}

//+------------------------------------------------------------------+
//| Analyze price action patterns                                  |
//+------------------------------------------------------------------+
double AnalyzePriceAction()
{
    double high1 = iHigh(_Symbol, PERIOD_M1, 1);
    double low1 = iLow(_Symbol, PERIOD_M1, 1);
    double close1 = iClose(_Symbol, PERIOD_M1, 1);
    double open1 = iOpen(_Symbol, PERIOD_M1, 1);

    if(high1 <= 0 || low1 <= 0) return 0.0;

    double bodySize = MathAbs(close1 - open1);
    double totalRange = high1 - low1;

    if(totalRange <= 0) return 0.0;

    double bodyRatio = bodySize / totalRange;

    // Strong bullish candle
    if(close1 > open1 && bodyRatio > 0.7)
        return 0.3;

    // Strong bearish candle
    if(close1 < open1 && bodyRatio > 0.7)
        return -0.3;

    // Moderate bullish
    if(close1 > open1 && bodyRatio > 0.5)
        return 0.2;

    // Moderate bearish
    if(close1 < open1 && bodyRatio > 0.5)
        return -0.2;

    return 0.0; // Neutral/weak price action
}

//+------------------------------------------------------------------+
//| Calculate market condition boost                               |
//+------------------------------------------------------------------+
double CalculateMarketConditionBoost()
{
    double boost = 0.0;

    // Check market momentum
    double momentum = CalculateMarketMomentum();
    if(MathAbs(momentum) > 0.3)
    {
        boost += 0.10;
        LogMessage("📈 STRONG MOMENTUM: +0.10 boost");
    }

    // Check price action confirmation
    double priceAction = AnalyzePriceAction();
    if(MathAbs(priceAction) > 0.2)
    {
        boost += 0.05;
        LogMessage("📊 PRICE ACTION CONFIRMATION: +0.05 boost");
    }

    return boost;
}

//+------------------------------------------------------------------+
//| Market Regime Detection Functions                               |
//+------------------------------------------------------------------+
RangeLevels DetectTradingRange()
{
    RangeLevels levels = {0};

    // Calculate 20-period high/low on M15 for more stable range detection
    double highest = iHigh(_Symbol, PERIOD_M15, iHighest(_Symbol, PERIOD_M15, MODE_HIGH, 20, 0));
    double lowest = iLow(_Symbol, PERIOD_M15, iLowest(_Symbol, PERIOD_M15, MODE_LOW, 20, 0));

    if(highest <= 0 || lowest <= 0)
    {
        levels.is_valid_range = false;
        return levels;
    }

    levels.resistance = highest;
    levels.support = lowest;
    levels.range_size = highest - lowest;
    levels.range_midpoint = (highest + lowest) / 2.0;
    levels.last_update = TimeCurrent();

    // Validate range using ATR
    double atr = CalculateATR();
    if(atr <= 0)
    {
        levels.is_valid_range = false;
        return levels;
    }

    // Range must be between 2-8 ATR periods (not too tight, not too wide)
    levels.is_valid_range = (levels.range_size > atr * 2.0 && levels.range_size < atr * 8.0);

    return levels;
}

//+------------------------------------------------------------------+
//| Detect market regime based on multiple factors                  |
//+------------------------------------------------------------------+
ENUM_MARKET_REGIME DetectMarketRegime()
{
    // Get ADX for trend strength
    double adxValue = GetADXValue();
    if(adxValue <= 0) return MARKET_UNCERTAIN;

    // Get current range levels
    RangeLevels range = DetectTradingRange();
    g_currentRange = range;

    // Get current price and volatility
    double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double atr = CalculateATR();
    double avgATR = CalculateAverageATR();
    double volatilityRatio = (avgATR > 0) ? atr / avgATR : 1.0;

    // Multi-timeframe trend analysis
    ENUM_SIGNAL_TYPE m5Trend = GetTrendDirection(PERIOD_M5);
    ENUM_SIGNAL_TYPE m15Trend = GetTrendDirection(PERIOD_M15);
    ENUM_SIGNAL_TYPE h1Trend = GetTrendDirection(PERIOD_H1);

    // Count aligned timeframes
    int bullishAlignment = 0;
    int bearishAlignment = 0;

    if(m5Trend == SIGNAL_BUY) bullishAlignment++;
    else if(m5Trend == SIGNAL_SELL) bearishAlignment++;

    if(m15Trend == SIGNAL_BUY) bullishAlignment++;
    else if(m15Trend == SIGNAL_SELL) bearishAlignment++;

    if(h1Trend == SIGNAL_BUY) bullishAlignment++;
    else if(h1Trend == SIGNAL_SELL) bearishAlignment++;

    // Regime classification logic
    if(adxValue < 20)
    {
        // Weak trend strength - likely ranging
        if(range.is_valid_range &&
           currentPrice > range.support + range.range_size * 0.1 &&
           currentPrice < range.resistance - range.range_size * 0.1)
        {
            g_regimeConfidence = 0.8;
            return MARKET_RANGING;
        }
        else
        {
            g_regimeConfidence = 0.4;
            return MARKET_UNCERTAIN;
        }
    }
    else if(adxValue > 30)
    {
        // Strong trend strength
        if(bullishAlignment >= 2)
        {
            // Check for breakout from range
            if(range.is_valid_range && currentPrice > range.resistance + range.range_size * 0.05)
            {
                g_regimeConfidence = 0.9;
                return MARKET_BREAKOUT;
            }
            else
            {
                g_regimeConfidence = 0.8;
                return MARKET_TRENDING_UP;
            }
        }
        else if(bearishAlignment >= 2)
        {
            // Check for breakout from range
            if(range.is_valid_range && currentPrice < range.support - range.range_size * 0.05)
            {
                g_regimeConfidence = 0.9;
                return MARKET_BREAKOUT;
            }
            else
            {
                g_regimeConfidence = 0.8;
                return MARKET_TRENDING_DOWN;
            }
        }
        else
        {
            g_regimeConfidence = 0.5;
            return MARKET_UNCERTAIN;
        }
    }
    else
    {
        // Moderate trend strength (20-30 ADX)
        if(bullishAlignment >= 2)
        {
            g_regimeConfidence = 0.6;
            return MARKET_TRENDING_UP;
        }
        else if(bearishAlignment >= 2)
        {
            g_regimeConfidence = 0.6;
            return MARKET_TRENDING_DOWN;
        }
        else if(range.is_valid_range)
        {
            g_regimeConfidence = 0.7;
            return MARKET_RANGING;
        }
        else
        {
            g_regimeConfidence = 0.3;
            return MARKET_UNCERTAIN;
        }
    }
}

//+------------------------------------------------------------------+
//| Check if breakout from range is valid                          |
//+------------------------------------------------------------------+
bool IsValidBreakout(RangeLevels range)
{
    if(!range.is_valid_range) return false;

    double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);

    // Volume confirmation
    long currentVolume = iVolume(_Symbol, PERIOD_M1, 0);
    long avgVolume = 0;
    for(int i = 1; i <= 20; i++)
    {
        avgVolume += iVolume(_Symbol, PERIOD_M1, i);
    }
    avgVolume /= 20;

    // Price breakout confirmation (must break by at least 10% of range size)
    bool priceBreakout = (currentPrice > range.resistance + range.range_size * 0.1) ||
                        (currentPrice < range.support - range.range_size * 0.1);

    // Volume confirmation (50% above average)
    bool volumeConfirmation = (avgVolume > 0) ? (currentVolume > avgVolume * 1.5) : true;

    // ADX confirmation (must be strong)
    double adxValue = GetADXValue();
    bool adxConfirmation = adxValue > 25;

    return priceBreakout && volumeConfirmation && adxConfirmation;
}

//+------------------------------------------------------------------+
//| Enhanced Signal Filtering Functions                             |
//+------------------------------------------------------------------+
double GetMinimumSignalThreshold(ENUM_MARKET_REGIME regime)
{
    switch(regime)
    {
        case MARKET_TRENDING_UP:
        case MARKET_TRENDING_DOWN:
            return 0.25; // Current threshold for trending markets

        case MARKET_RANGING:
            return 0.60; // Much higher threshold for ranging markets

        case MARKET_BREAKOUT:
            return 0.45; // Moderate threshold for breakouts

        case MARKET_UNCERTAIN:
            return 0.50; // Conservative threshold for uncertain conditions

        default:
            return 0.40; // Safe default
    }
}

//+------------------------------------------------------------------+
//| Get required confluence signals based on market regime          |
//+------------------------------------------------------------------+
int GetRequiredConfluenceSignals(ENUM_MARKET_REGIME regime)
{
    switch(regime)
    {
        case MARKET_TRENDING_UP:
        case MARKET_TRENDING_DOWN:
            return 2; // Minimum 2/3 timeframe alignment for trending

        case MARKET_RANGING:
            return 3; // Require 3/3 timeframe alignment for ranging

        case MARKET_BREAKOUT:
            return 2; // Minimum 2/3 with strong momentum for breakouts

        case MARKET_UNCERTAIN:
            return 3; // Conservative - require full alignment

        default:
            return 2; // Safe default
    }
}

//+------------------------------------------------------------------+
//| Check if volatility is optimal for the current market regime    |
//+------------------------------------------------------------------+
bool IsVolatilityOptimal(ENUM_MARKET_REGIME regime)
{
    double atr = CalculateATR();
    double avgATR = CalculateAverageATR();

    if(avgATR <= 0) return true; // Fallback if ATR calculation fails

    double volatilityRatio = atr / avgATR;

    switch(regime)
    {
        case MARKET_TRENDING_UP:
        case MARKET_TRENDING_DOWN:
            // Allow lower volatility for established trends
            return volatilityRatio > 0.8;

        case MARKET_RANGING:
            // Avoid high volatility ranges (false breakouts)
            return volatilityRatio < 1.2 && volatilityRatio > 0.6;

        case MARKET_BREAKOUT:
            // Require high volatility for valid breakouts
            return volatilityRatio > 1.5;

        case MARKET_UNCERTAIN:
            // Conservative - require normal volatility
            return volatilityRatio > 0.9 && volatilityRatio < 1.3;

        default:
            return volatilityRatio > 1.0;
    }
}

//+------------------------------------------------------------------+
//| Check if we should avoid trading in current market conditions   |
//+------------------------------------------------------------------+
bool ShouldAvoidTrading(ENUM_MARKET_REGIME regime)
{
    // Check for consecutive ranging losses
    if(regime == MARKET_RANGING && g_consecutiveRangingLosses >= g_rangingLossThreshold)
    {
        LogMessage("🚫 AVOIDING RANGING TRADES: " + IntegerToString(g_consecutiveRangingLosses) + " consecutive losses");
        return true;
    }

    // Check volatility conditions
    if(!IsVolatilityOptimal(regime))
    {
        LogMessage("🚫 AVOIDING TRADE: Volatility not optimal for " + EnumToString(regime));
        return true;
    }

    // Check regime confidence
    if(g_regimeConfidence < 0.4)
    {
        LogMessage("🚫 AVOIDING TRADE: Low regime confidence (" + DoubleToString(g_regimeConfidence, 2) + ")");
        return true;
    }

    // Additional ranging market protection
    if(regime == MARKET_RANGING)
    {
        // Check if we're too close to range boundaries (avoid false breakouts)
        double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
        if(g_currentRange.is_valid_range)
        {
            double distanceFromSupport = currentPrice - g_currentRange.support;
            double distanceFromResistance = g_currentRange.resistance - currentPrice;
            double minDistance = g_currentRange.range_size * 0.15; // 15% of range size

            if(distanceFromSupport < minDistance || distanceFromResistance < minDistance)
            {
                LogMessage("🚫 AVOIDING RANGING TRADE: Too close to range boundary");
                return true;
            }
        }
    }

    return false;
}

//+------------------------------------------------------------------+
//| PHASE 2: Calculate win streak momentum multiplier               |
//+------------------------------------------------------------------+
double CalculateWinStreakMomentum(int chainId = -1)
{
    int consecutiveWins = 0;

    // Get consecutive wins from appropriate source
    if(chainId > 0 && chainId <= 3)
    {
        int chainIndex = chainId - 1;
        consecutiveWins = g_orderChains[chainIndex].consecutiveWins;
    }
    else
    {
        consecutiveWins = g_consecutiveWins; // Global state
    }

    // PHASE 2: Win streak momentum (+10% per consecutive win after 3rd, capped at 50%)
    double momentumMultiplier = 1.0;

    if(consecutiveWins >= 3)
    {
        // +10% per win after the 3rd win
        double bonusWins = consecutiveWins - 2; // Wins beyond the 2nd
        double bonusPercentage = bonusWins * 0.1; // 10% per bonus win

        // Cap at 50% bonus (5 bonus wins)
        bonusPercentage = MathMin(bonusPercentage, 0.5);

        momentumMultiplier = 1.0 + bonusPercentage;

        LogMessage(StringFormat("🚀 WIN STREAK MOMENTUM: %d wins → %.1f%% bonus (multiplier: %.2f)",
                  consecutiveWins, bonusPercentage * 100, momentumMultiplier));
    }

    return momentumMultiplier;
}

//+------------------------------------------------------------------+
//| Dynamic Risk Management Functions                               |
//+------------------------------------------------------------------+
double CalculateQualityAdjustedLot(double baseLot, double signalStrength)
{
    double qualityMultiplier = 1.0;

    if(signalStrength >= 0.40)
    {
        qualityMultiplier = 1.3;  // Strong signal - increase size
        LogMessage("🎯 HIGH QUALITY SIGNAL - Lot increased by 30%");
    }
    else if(signalStrength >= 0.25)
    {
        qualityMultiplier = 1.0;  // Normal signal - standard size
        LogMessage("✅ STANDARD QUALITY SIGNAL - Normal lot size");
    }
    else if(signalStrength >= 0.15)
    {
        qualityMultiplier = 0.7;  // Weak signal - reduce size
        LogMessage("⚠️ WEAK SIGNAL - Lot reduced by 30%");
    }
    else
    {
        qualityMultiplier = 0.5;  // Very weak - minimum size
        LogMessage("🔻 VERY WEAK SIGNAL - Lot reduced by 50%");
    }

    double adjustedLot = baseLot * qualityMultiplier;

    // Apply broker limits
    double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    adjustedLot = MathMax(adjustedLot, minLot);
    adjustedLot = MathMin(adjustedLot, maxLot);
    adjustedLot = NormalizeDouble(adjustedLot / lotStep, 0) * lotStep;

    return adjustedLot;
}

//+------------------------------------------------------------------+
//| Calculate adaptive SL/TP distances                             |
//+------------------------------------------------------------------+
void CalculateAdaptiveSLTP(double signalStrength, double &slDistance, double &tpDistance)
{
    // OPTIMIZED: Based on observation that orders reach +$8-9 then reverse
    // Adjusting TP targets to capture profits before reversal
    double baseTP = 9.0;  // Reduced from $15 to $9 to capture observed profits
    double baseSL = 6.0;  // Reduced from $10 to $6 to maintain better R:R

    if(signalStrength >= 0.40)
    {
        // High confidence - tighter SL, optimized TP for +$8-9 capture
        slDistance = baseSL * 0.8;   // $4.8 risk
        tpDistance = baseTP * 1.0;   // $9.0 target (1:1.88 R:R) - optimized for reversal zone
        LogMessage("🎯 HIGH CONFIDENCE: SL=$" + DoubleToString(slDistance, 2) + ", TP=$" + DoubleToString(tpDistance, 2) + " (Optimized for +$8-9 capture)");
    }
    else if(signalStrength >= 0.25)
    {
        // Standard confidence - balanced ratios for profit capture
        slDistance = baseSL;         // $6.0 risk
        tpDistance = baseTP * 0.9;   // $8.1 target (1:1.35 R:R) - just below reversal zone
        LogMessage("✅ STANDARD CONFIDENCE: SL=$" + DoubleToString(slDistance, 2) + ", TP=$" + DoubleToString(tpDistance, 2) + " (Optimized for +$8-9 capture)");
    }
    else if(signalStrength >= 0.15)
    {
        // Low confidence - conservative TP to avoid reversal
        slDistance = baseSL * 1.2;   // $7.2 risk
        tpDistance = baseTP * 0.8;   // $7.2 target (1:1.0 R:R) - safe zone before reversal
        LogMessage("⚠️ LOW CONFIDENCE: SL=$" + DoubleToString(slDistance, 2) + ", TP=$" + DoubleToString(tpDistance, 2) + " (Conservative capture before reversal)");
    }
    else
    {
        // Very low confidence - very conservative approach
        slDistance = baseSL * 1.5;   // $9.0 risk
        tpDistance = baseTP * 0.7;   // $6.3 target (1:0.7 R:R) - quick profit capture
        LogMessage("🔻 VERY LOW CONFIDENCE: SL=$" + DoubleToString(slDistance, 2) + ", TP=$" + DoubleToString(tpDistance, 2) + " (Quick capture before reversal)");
    }
}

//+------------------------------------------------------------------+
//| Calculate smart interval based on signal quality               |
//+------------------------------------------------------------------+
int CalculateSmartInterval(double signalStrength)
{
    int baseInterval = 30; // Base 30 seconds

    if(signalStrength >= 0.40)
    {
        return (int)(baseInterval * 0.5); // 15 seconds
    }
    else if(signalStrength >= 0.25)
    {
        return baseInterval; // 30 seconds
    }
    else if(signalStrength >= 0.15)
    {
        return (int)(baseInterval * 1.5); // 45 seconds
    }
    else
    {
        return baseInterval * 2; // 60 seconds
    }
}

//+------------------------------------------------------------------+
//| Check if optimal entry time based on market conditions         |
//+------------------------------------------------------------------+
bool IsOptimalEntryTime()
{
    double currentATR = CalculateATR();
    double averageATR = CalculateAverageATR();

    if(averageATR <= 0) return true; // Fallback

    double volatilityRatio = currentATR / averageATR;

    if(volatilityRatio > 2.0)
    {
        LogMessage("⚠️ EXTREME VOLATILITY - Delaying entry by 30s");
        return false; // Wait for calmer conditions
    }

    if(volatilityRatio > 1.5)
    {
        LogMessage("📊 HIGH VOLATILITY - Using conservative parameters");
    }

    return true; // Proceed with entry
}

//+------------------------------------------------------------------+
//| Regime-Based Risk Management Functions                          |
//+------------------------------------------------------------------+
void CalculateRegimeBasedSLTP(ENUM_MARKET_REGIME regime, double signalStrength,
                              double &slDistance, double &tpDistance)
{
    // CRITICAL: M1 SCALPING OPTIMIZED - Use realistic base targets
    string symbol = Symbol();
    double baseTP = 3.0; // Default $3 base for M1 scalping
    double baseSL = 2.0; // Default $2 base SL for M1 scalping

    if(StringFind(symbol, "BTC") >= 0)
    {
        baseTP = 5.0; // $5 base for BTC due to higher volatility
        baseSL = 3.0; // $3 base SL for BTC
    }
    else if(StringFind(symbol, "XAU") >= 0 || StringFind(symbol, "GOLD") >= 0)
    {
        baseTP = 4.0; // $4 base for XAU
        baseSL = 2.5; // $2.5 base SL for XAU
    }

    switch(regime)
    {
        case MARKET_TRENDING_UP:
        case MARKET_TRENDING_DOWN:
            // M1 SCALPING OPTIMIZED - Realistic trending targets
            if(signalStrength >= 0.40)
            {
                slDistance = baseSL * 0.8;   // Reduced risk for high confidence
                tpDistance = baseTP * 1.2;   // Slightly higher target for strong trends
                LogMessage("📈 HIGH TRENDING M1: SL=$" + DoubleToString(slDistance, 2) + ", TP=$" + DoubleToString(tpDistance, 2) + " (R:R=1:" + DoubleToString(tpDistance/slDistance, 2) + ")");
            }
            else if(signalStrength >= 0.25)
            {
                slDistance = baseSL;         // Standard risk
                tpDistance = baseTP;         // Standard target
                LogMessage("📈 STANDARD TRENDING M1: SL=$" + DoubleToString(slDistance, 2) + ", TP=$" + DoubleToString(tpDistance, 2) + " (R:R=1:" + DoubleToString(tpDistance/slDistance, 2) + ")");
            }
            else
            {
                slDistance = baseSL * 1.1;   // Slightly higher risk for weak signals
                tpDistance = baseTP * 0.9;   // Conservative target
                LogMessage("📈 WEAK TRENDING M1: SL=$" + DoubleToString(slDistance, 2) + ", TP=$" + DoubleToString(tpDistance, 2) + " (R:R=1:" + DoubleToString(tpDistance/slDistance, 2) + ")");
            }
            break;

        case MARKET_RANGING:
            // Conservative ranging - capture smaller but reliable profits
            if(signalStrength >= 0.70)
            {
                slDistance = baseSL * 1.0;   // $6.0 risk
                tpDistance = baseTP * 0.9;   // $8.1 target (1:1.35 R:R)
                LogMessage("🔄 HIGH QUALITY RANGING: SL=$" + DoubleToString(slDistance, 2) + ", TP=$" + DoubleToString(tpDistance, 2) + " (Quick profit capture)");
            }
            else if(signalStrength >= 0.60)
            {
                slDistance = baseSL * 1.2;   // $7.2 risk
                tpDistance = baseTP * 0.8;   // $7.2 target (1:1.0 R:R)
                LogMessage("🔄 STANDARD RANGING: SL=$" + DoubleToString(slDistance, 2) + ", TP=$" + DoubleToString(tpDistance, 2) + " (Quick profit capture)");
            }
            else
            {
                slDistance = baseSL * 1.5;   // $9.0 risk
                tpDistance = baseTP * 0.7;   // $6.3 target (1:0.7 R:R)
                LogMessage("🔄 WEAK RANGING: SL=$" + DoubleToString(slDistance, 2) + ", TP=$" + DoubleToString(tpDistance, 2) + " (Conservative capture)");
            }
            break;

        case MARKET_BREAKOUT:
            // Breakout - try to capture more but still realistic
            if(signalStrength >= 0.60)
            {
                slDistance = baseSL * 0.7;   // $4.2 risk
                tpDistance = baseTP * 1.4;   // $12.6 target (1:3.0 R:R)
                LogMessage("🚀 STRONG BREAKOUT: SL=$" + DoubleToString(slDistance, 2) + ", TP=$" + DoubleToString(tpDistance, 2) + " (Extended target for breakout)");
            }
            else
            {
                slDistance = baseSL * 0.8;   // $4.8 risk
                tpDistance = baseTP * 1.2;   // $10.8 target (1:2.25 R:R)
                LogMessage("🚀 MODERATE BREAKOUT: SL=$" + DoubleToString(slDistance, 2) + ", TP=$" + DoubleToString(tpDistance, 2) + " (Moderate breakout target)");
            }
            break;

        case MARKET_UNCERTAIN:
        default:
            // Very conservative - capture any profit quickly
            slDistance = baseSL * 1.3;   // $7.8 risk
            tpDistance = baseTP * 0.8;   // $7.2 target (1:0.92 R:R)
            LogMessage("❓ UNCERTAIN MARKET: SL=$" + DoubleToString(slDistance, 2) + ", TP=$" + DoubleToString(tpDistance, 2) + " (Quick profit capture)");
            break;
    }
}

//+------------------------------------------------------------------+
//| Calculate regime-based position sizing                          |
//+------------------------------------------------------------------+
double CalculateRegimeBasedLotSize(double baseLot, ENUM_MARKET_REGIME regime, double signalStrength)
{
    double regimeMultiplier = 1.0;

    switch(regime)
    {
        case MARKET_TRENDING_UP:
        case MARKET_TRENDING_DOWN:
            // Standard position sizing for trending markets
            regimeMultiplier = 1.0;
            LogMessage("📈 TRENDING POSITION SIZE: Standard (1.0x)");
            break;

        case MARKET_RANGING:
            // Reduced position size for ranging markets (50% reduction)
            regimeMultiplier = 0.5;
            LogMessage("🔄 RANGING POSITION SIZE: Reduced (0.5x)");
            break;

        case MARKET_BREAKOUT:
            // Increased position size for breakouts (30% increase)
            regimeMultiplier = 1.3;
            LogMessage("🚀 BREAKOUT POSITION SIZE: Increased (1.3x)");
            break;

        case MARKET_UNCERTAIN:
        default:
            // Conservative position size for uncertain conditions (30% reduction)
            regimeMultiplier = 0.7;
            LogMessage("❓ UNCERTAIN POSITION SIZE: Conservative (0.7x)");
            break;
    }

    // Apply quality adjustment on top of regime adjustment
    double qualityAdjustedLot = CalculateQualityAdjustedLot(baseLot, signalStrength);
    double finalLot = qualityAdjustedLot * regimeMultiplier;

    // Apply broker limits
    double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    finalLot = MathMax(finalLot, minLot);
    finalLot = MathMin(finalLot, maxLot);
    finalLot = NormalizeDouble(finalLot / lotStep, 0) * lotStep;

    LogMessage(StringFormat("🎯 REGIME LOT CALCULATION: Base=%.3f, Quality=%.3f, Regime=%.1fx, Final=%.3f",
               baseLot, qualityAdjustedLot, regimeMultiplier, finalLot));

    return finalLot;
}

//+------------------------------------------------------------------+
//| Calculate regime-based smart interval                           |
//+------------------------------------------------------------------+
int CalculateRegimeBasedInterval(ENUM_MARKET_REGIME regime, double signalStrength)
{
    int baseInterval = 30; // Base 30 seconds

    switch(regime)
    {
        case MARKET_TRENDING_UP:
        case MARKET_TRENDING_DOWN:
            // Use existing smart interval logic for trending
            return CalculateSmartInterval(signalStrength);

        case MARKET_RANGING:
            // Slower intervals for ranging markets (avoid overtrading)
            if(signalStrength >= 0.70)
                return baseInterval * 2; // 60 seconds
            else if(signalStrength >= 0.60)
                return baseInterval * 3; // 90 seconds
            else
                return baseInterval * 4; // 120 seconds

        case MARKET_BREAKOUT:
            // Faster intervals for breakouts (capture momentum)
            return (int)(baseInterval * 0.5); // 15 seconds

        case MARKET_UNCERTAIN:
        default:
            // Conservative intervals for uncertain conditions
            return baseInterval * 2; // 60 seconds
    }
}

//+------------------------------------------------------------------+
//| CRITICAL: Quick profit capture logic for M1 scalping            |
//+------------------------------------------------------------------+
bool ShouldTakeQuickProfit(int orderIndex, double currentProfit)
{
    if(orderIndex < 0 || orderIndex >= ArraySize(g_orders)) return false;
    if(!PositionSelectByTicket(g_orders[orderIndex].ticket)) return false;

    double targetProfit = g_orders[orderIndex].initialTPTarget;
    if(targetProfit <= 0) return false;

    // CRITICAL: Take profit at 60-70% of target if showing reversal signs
    if(currentProfit >= targetProfit * 0.6)
    {
        // Check for reversal indicators
        double rsi[];
        ArraySetAsSeries(rsi, true);
        if(CopyBuffer(g_rsiHandle, 0, 0, 1, rsi) >= 1)
        {
            double rsiValue = rsi[0];
            if(rsiValue > 70 || rsiValue < 30) // Overbought/oversold
            {
                LogMessage(StringFormat("🎯 QUICK PROFIT (RSI): Closing at $%.2f (%.1f%% of target, RSI=%.1f)",
                          currentProfit, (currentProfit/targetProfit)*100, rsiValue));
                return true;
            }
        }

        // Check price action reversal pattern
        if(IsShowingReversalPattern(orderIndex))
        {
            LogMessage(StringFormat("🎯 QUICK PROFIT (REVERSAL): Closing at $%.2f (%.1f%% of target)",
                      currentProfit, (currentProfit/targetProfit)*100));
            return true;
        }

        // Take profit at 70% if no clear signals but good profit achieved
        if(currentProfit >= targetProfit * 0.7)
        {
            LogMessage(StringFormat("🎯 QUICK PROFIT (70%%): Closing at $%.2f (%.1f%% of target)",
                      currentProfit, (currentProfit/targetProfit)*100));
            return true;
        }
    }

    return false;
}

//+------------------------------------------------------------------+
//| Check for reversal pattern in price action                      |
//+------------------------------------------------------------------+
bool IsShowingReversalPattern(int orderIndex)
{
    double high[], low[], close[];
    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    ArraySetAsSeries(close, true);

    if(CopyHigh(_Symbol, PERIOD_M1, 0, 3, high) < 3 ||
       CopyLow(_Symbol, PERIOD_M1, 0, 3, low) < 3 ||
       CopyClose(_Symbol, PERIOD_M1, 0, 3, close) < 3)
        return false;

    int orderType = g_orders[orderIndex].type;

    if(orderType == ORDER_TYPE_BUY)
    {
        // For BUY orders, check if price is pulling back from recent high
        if(close[0] < high[0] * 0.9995 && close[0] < close[1]) // 0.05% pullback + declining
            return true;
    }
    else
    {
        // For SELL orders, check if price is pulling back from recent low
        if(close[0] > low[0] * 1.0005 && close[0] > close[1]) // 0.05% pullback + rising
            return true;
    }

    return false;
}

//+------------------------------------------------------------------+
//| Enhanced Profit Protection for +$8-9 Observation Pattern       |
//+------------------------------------------------------------------+
bool ShouldCloseForProfitProtection(int orderIndex, double currentProfit)
{
    if(orderIndex < 0 || orderIndex >= ArraySize(g_orders)) return false;

    // Check if the order position still exists (instead of isActive field)
    if(!PositionSelectByTicket(g_orders[orderIndex].ticket)) return false;

    // CRITICAL: First check quick profit logic for M1 scalping
    if(ShouldTakeQuickProfit(orderIndex, currentProfit))
        return true;

    // CRITICAL: If profit reaches $7.5+, start aggressive protection
    if(currentProfit >= 7.5)
    {
        // Get current price movement direction
        double currentPrice = (g_orders[orderIndex].type == ORDER_TYPE_BUY) ?
                             SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                             SymbolInfoDouble(_Symbol, SYMBOL_ASK);
        double openPrice = g_orders[orderIndex].openPrice;

        // Calculate price movement from open
        double priceMovement = (g_orders[orderIndex].type == ORDER_TYPE_BUY) ?
                              (currentPrice - openPrice) : (openPrice - currentPrice);

        // If we're at +$7.5-8.5 and price starts reversing, close immediately
        if(currentProfit >= 7.5 && currentProfit <= 8.5)
        {
            // Check for reversal signs using recent price action
            double recentHigh = iHigh(_Symbol, PERIOD_M1, 0);
            double recentLow = iLow(_Symbol, PERIOD_M1, 0);
            double prevHigh = iHigh(_Symbol, PERIOD_M1, 1);
            double prevLow = iLow(_Symbol, PERIOD_M1, 1);

            bool showingReversal = false;

            if(g_orders[orderIndex].type == ORDER_TYPE_BUY)
            {
                // For BUY orders, check if price is pulling back from recent high
                showingReversal = (currentPrice < recentHigh * 0.9995); // 0.05% pullback
            }
            else
            {
                // For SELL orders, check if price is pulling back from recent low
                showingReversal = (currentPrice > recentLow * 1.0005); // 0.05% pullback
            }

            if(showingReversal)
            {
                LogMessage(StringFormat("🎯 PROFIT PROTECTION: Closing at $%.2f profit (reversal detected at +$7.5-8.5 zone)", currentProfit));
                return true;
            }
        }

        // If profit reaches $8.5+, close immediately regardless (observed reversal zone)
        if(currentProfit >= 8.5)
        {
            LogMessage(StringFormat("🎯 PROFIT PROTECTION: Closing at $%.2f profit (reached reversal zone)", currentProfit));
            return true;
        }
    }

    // Additional protection: If profit reaches $6+ and we're in ranging market, be more aggressive
    if(currentProfit >= 6.0 && g_currentMarketRegime == MARKET_RANGING)
    {
        // In ranging markets, take profits more aggressively at $6+
        if(currentProfit >= 7.0)
        {
            LogMessage(StringFormat("🔄 RANGING PROTECTION: Closing at $%.2f profit (ranging market quick capture)", currentProfit));
            return true;
        }
    }

    return false;
}

//+------------------------------------------------------------------+
//| Get next available order chain for new order                    |
//+------------------------------------------------------------------+
int GetNextAvailableChain()
{
    // Find an inactive chain
    for(int i = 0; i < 3; i++)
    {
        if(!g_orderChains[i].isActive)
        {
            return g_orderChains[i].chainId;
        }
    }

    // If all chains are active, return -1 (no available chain)
    return -1;
}

//+------------------------------------------------------------------+
//| Get chain by ID                                                  |
//+------------------------------------------------------------------+
int GetChainIndex(int chainId)
{
    if(chainId >= 1 && chainId <= 3)
        return chainId - 1;
    return -1;
}

//+------------------------------------------------------------------+
//| Activate order chain                                             |
//+------------------------------------------------------------------+
void ActivateOrderChain(int chainId, ulong ticket)
{
    int chainIndex = GetChainIndex(chainId);
    if(chainIndex >= 0)
    {
        g_orderChains[chainIndex].isActive = true;
        g_orderChains[chainIndex].activeTicket = ticket;
        g_orderChains[chainIndex].lastTradeTime = TimeCurrent();
        LogMessage(StringFormat("✅ Chain %d activated with ticket %d", chainId, ticket));
    }
}

//+------------------------------------------------------------------+
//| Deactivate order chain                                           |
//+------------------------------------------------------------------+
void DeactivateOrderChain(int chainId)
{
    int chainIndex = GetChainIndex(chainId);
    if(chainIndex >= 0)
    {
        g_orderChains[chainIndex].isActive = false;
        g_orderChains[chainIndex].activeTicket = 0;
        LogMessage(StringFormat("🔄 Chain %d deactivated", chainId));
    }
}

//+------------------------------------------------------------------+
//| Update chain Anti-Martingale counters                            |
//+------------------------------------------------------------------+
void UpdateChainCounters(int chainId, bool isWin, double profit)
{
    int chainIndex = GetChainIndex(chainId);
    if(chainIndex < 0) return;

    OrderChain chain = g_orderChains[chainIndex];

    if(isWin)
    {
        chain.consecutiveWins++;
        chain.consecutiveLosses = 0;
        chain.winStreak += profit;
        chain.isInWinningStreak = true;
        chain.lastTradeProfit = profit;
        chain.currentStep = MathMin(chain.consecutiveWins - 1, g_configuredMaxAntiMartingaleStep - 1);

        LogMessage(StringFormat("Chain %d WIN: %d consecutive, Step %d, Streak: $%.2f",
                  chainId, chain.consecutiveWins, chain.currentStep + 1, chain.winStreak));
    }
    else
    {
        chain.consecutiveWins = 0;
        chain.consecutiveLosses++;
        chain.winStreak = 0.0;
        chain.isInWinningStreak = false;
        chain.lastTradeProfit = profit; // Negative value
        chain.currentStep = 0;

        LogMessage(StringFormat("Chain %d LOSS: %d consecutive, Reset to Step 1",
                  chainId, chain.consecutiveLosses));
    }
}

//+------------------------------------------------------------------+
//| Calculate SL/TP based on realistic M1 scalping distances        |
//+------------------------------------------------------------------+
void CalculateStopLevels(int orderType, double price, double &sl, double &tp, double lotSize, int chainId = -1)
{
    double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
    int digits = (int)SymbolInfoInteger(Symbol(), SYMBOL_DIGITS);
    string symbol = Symbol();

    // FIXED: Use realistic point-based distances for M1 scalping
    double profitPoints = 0;
    double lossPoints = 0;

    // Determine current Anti-Martingale step with maximum limit (chain-specific or global)
    int stepIndex = 0;
    if(chainId > 0 && chainId <= 3)
    {
        int chainIndex = chainId - 1;
        stepIndex = g_orderChains[chainIndex].currentStep;
    }
    else
    {
        stepIndex = g_currentStep;
    }
    stepIndex = MathMin(stepIndex, ArraySize(g_profitSteps) - 1);
    stepIndex = MathMin(stepIndex, g_configuredMaxAntiMartingaleStep - 1);

    // Calculate point value FIRST (needed for profit calculations)
    double pointValuePerLot = GetPointValuePerLot();
    double pointValueForThisLot = pointValuePerLot * lotSize;

    // CRITICAL: M1 SCALPING OPTIMIZED - Realistic Base Targets with Fibonacci Progression
    string symbol = Symbol();
    double baseTarget = 3.0; // Default $3 base for M1 scalping
    if(StringFind(symbol, "BTC") >= 0)
    {
        baseTarget = 5.0; // $5 base for BTC due to higher volatility
    }
    else if(StringFind(symbol, "XAU") >= 0 || StringFind(symbol, "GOLD") >= 0)
    {
        baseTarget = 4.0; // $4 base for XAU
    }

    // Apply Fibonacci progression to realistic base target
    double rawTargetProfit = baseTarget * g_profitSteps[stepIndex];

    // CRITICAL: Apply spread awareness to profit target
    double targetProfitAmount = CalculateSpreadAwareProfitTarget(rawTargetProfit);
    double targetLossAmount = 0.0; // Will be calculated based on Anti-Martingale protection

    LogMessage(StringFormat("M1 TARGET CALC: Base=$%.2f, Raw=$%.2f, Spread-Aware=$%.2f (Step %d)",
              baseTarget, rawTargetProfit, targetProfitAmount, stepIndex + 1));

    //--- Apply regime-based dynamic SL/TP
    double regimeSLAmount = targetLossAmount;
    double regimeTPAmount = targetProfitAmount;

    if(g_currentSignalStrength > 0)
    {
        // Calculate regime-based SL/TP distances
        CalculateRegimeBasedSLTP(g_currentMarketRegime, g_currentSignalStrength, regimeSLAmount, regimeTPAmount);

        LogMessage(StringFormat("🎯 REGIME SL/TP: Original SL=$%.2f TP=$%.2f, Regime SL=$%.2f TP=$%.2f (Signal: %.3f, Regime: %s)",
                   targetLossAmount, targetProfitAmount, regimeSLAmount, regimeTPAmount, g_currentSignalStrength,
                   g_currentMarketRegime == MARKET_RANGING ? "RANGING" :
                   g_currentMarketRegime == MARKET_TRENDING_UP ? "TRENDING_UP" :
                   g_currentMarketRegime == MARKET_TRENDING_DOWN ? "TRENDING_DOWN" :
                   g_currentMarketRegime == MARKET_BREAKOUT ? "BREAKOUT" : "UNCERTAIN"));

        // Update target amounts
        targetLossAmount = regimeSLAmount;
        targetProfitAmount = regimeTPAmount;

        // Store for monitoring
        g_adaptiveSLDistance = regimeSLAmount;
        g_adaptiveTPDistance = regimeTPAmount;
    }

    // CRITICAL: Calculate realistic point distances for M1 scalping
    if(StringFind(symbol, "BTC") >= 0)
    {
        // BTC: Calculate points needed for target profit amount
        // CRITICAL: Reduced from 5000 to 1000-2000 for realistic M1 scalping
        double pointsNeededForProfit = targetProfitAmount / pointValueForThisLot;
        profitPoints = MathMax(pointsNeededForProfit, 1000); // OPTIMIZED: Minimum 1000 for M1 scalping

        LogMessage(StringFormat("BTC M1 POINTS: Target=$%.2f, PointValue=%.6f, Required=%.0f, Final=%.0f",
                  targetProfitAmount, pointValueForThisLot, pointsNeededForProfit, profitPoints));

        // Anti-Martingale SL: Limit loss to previous trade profit (chain-specific or global)
        if(chainId > 0 && chainId <= 3)
        {
            int chainIndex = chainId - 1;
            if(g_orderChains[chainIndex].consecutiveWins > 0 && g_orderChains[chainIndex].lastTradeProfit > 0)
            {
                // SL ≤ 95% of previous trade profit (Anti-Martingale protection)
                double maxAllowedLoss = g_orderChains[chainIndex].lastTradeProfit * 0.95;
                targetLossAmount = MathMin(targetProfitAmount * 0.6, maxAllowedLoss);
            }
            else
            {
                // PHASE 2: Volatility-adjusted SL for first trade
                double baseSL = targetProfitAmount * 0.6; // Base 60% of profit target
                targetLossAmount = CalculateVolatilityAdjustedSL(baseSL);
            }
        }
        else
        {
            // Legacy global state
            if(g_consecutiveWins > 0 && g_currentWinStreak > 0)
            {
                double maxAllowedLoss = (g_currentWinStreak / g_consecutiveWins) * 0.9;
                targetLossAmount = MathMin(targetProfitAmount * 0.6, maxAllowedLoss);
            }
            else
            {
                // PHASE 2: Volatility-adjusted SL for legacy global state
                double baseSL = targetProfitAmount * 0.6;
                targetLossAmount = CalculateVolatilityAdjustedSL(baseSL);
            }
        }

        double pointsNeededForLoss = targetLossAmount / pointValueForThisLot;
        lossPoints = MathMax(pointsNeededForLoss, 600); // OPTIMIZED: Reduced from 3000 to 600 for M1 scalping
    }
    else if(StringFind(symbol, "XAU") >= 0 || StringFind(symbol, "GOLD") >= 0)
    {
        // XAU: Calculate points needed for target profit amount
        // CRITICAL: Optimized for M1 scalping while maintaining broker compliance
        double pointsNeededForProfit = targetProfitAmount / pointValueForThisLot;
        profitPoints = MathMax(pointsNeededForProfit, 80); // OPTIMIZED: Reduced from 150 to 80 for M1 scalping

        LogMessage(StringFormat("XAU M1 POINTS: Target=$%.2f, PointValue=%.6f, Required=%.0f, Final=%.0f",
                  targetProfitAmount, pointValueForThisLot, pointsNeededForProfit, profitPoints));

        // Anti-Martingale SL: Limit loss to previous trade profit (chain-specific or global)
        if(chainId > 0 && chainId <= 3)
        {
            int chainIndex = chainId - 1;
            if(g_orderChains[chainIndex].consecutiveWins > 0 && g_orderChains[chainIndex].lastTradeProfit > 0)
            {
                // SL ≤ 95% of previous trade profit (Anti-Martingale protection)
                double maxAllowedLoss = g_orderChains[chainIndex].lastTradeProfit * 0.95;
                targetLossAmount = MathMin(targetProfitAmount * 0.6, maxAllowedLoss);
            }
            else
            {
                // PHASE 2: Volatility-adjusted SL for first trade
                double baseSL = targetProfitAmount * 0.6; // Base 60% of profit target
                targetLossAmount = CalculateVolatilityAdjustedSL(baseSL);
            }
        }
        else
        {
            // Legacy global state
            if(g_consecutiveWins > 0 && g_currentWinStreak > 0)
            {
                double maxAllowedLoss = (g_currentWinStreak / g_consecutiveWins) * 0.9;
                targetLossAmount = MathMin(targetProfitAmount * 0.6, maxAllowedLoss);
            }
            else
            {
                // PHASE 2: Volatility-adjusted SL for legacy global state
                double baseSL = targetProfitAmount * 0.6;
                targetLossAmount = CalculateVolatilityAdjustedSL(baseSL);
            }
        }

        double pointsNeededForLoss = targetLossAmount / pointValueForThisLot;
        lossPoints = MathMax(pointsNeededForLoss, 50); // OPTIMIZED: Reduced from 100 to 50 for M1 scalping
    }
    else
    {
        // Default calculation for other instruments - OPTIMIZED for M1 scalping
        double pointsNeededForProfit = targetProfitAmount / pointValueForThisLot;
        profitPoints = MathMax(pointsNeededForProfit, 100); // OPTIMIZED: Reduced from 200 to 100 for M1 scalping

        LogMessage(StringFormat("DEFAULT M1 POINTS: Target=$%.2f, PointValue=%.6f, Required=%.0f, Final=%.0f",
                  targetProfitAmount, pointValueForThisLot, pointsNeededForProfit, profitPoints));

        // PHASE 2: Volatility-adjusted SL for default instruments
        double baseSL = targetProfitAmount * 0.6;
        targetLossAmount = CalculateVolatilityAdjustedSL(baseSL);

        double pointsNeededForLoss = targetLossAmount / pointValueForThisLot;
        lossPoints = MathMax(pointsNeededForLoss, 75); // OPTIMIZED: Reduced from 150 to 75 for M1 scalping
    }

    // Recalculate actual target amounts based on final point distances
    double actualProfitAmount = profitPoints * pointValueForThisLot;
    double actualLossAmount = lossPoints * pointValueForThisLot;

    // ENHANCED ANTI-MARTINGALE PROTECTION: Critical SL ≤ Previous Profit Rule
    if(g_consecutiveWins > 0)
    {
        // Get the profit from the last winning trade
        double lastTradeProfit = 0.0;
        if(g_tradeHistoryIndex > 0)
        {
            int lastIndex = (g_tradeHistoryIndex - 1) % 10;
            if(g_recentTrades[lastIndex].isWin)
            {
                lastTradeProfit = g_recentTrades[lastIndex].profit;
            }
        }

        // CRITICAL ANTI-MARTINGALE RULE: Next trade's SL ≤ previous trade's profit
        if(lastTradeProfit > 0 && actualLossAmount > lastTradeProfit)
        {
            double adjustedLossAmount = lastTradeProfit * 0.95; // Use 95% of last profit as max loss
            // Recalculate loss points based on adjusted target
            lossPoints = adjustedLossAmount / pointValueForThisLot;
            actualLossAmount = adjustedLossAmount;
            LogMessage(StringFormat("🛡️ ANTI-MARTINGALE PROTECTION: SL limited to $%.2f (95%% of last profit $%.2f)",
                      adjustedLossAmount, lastTradeProfit));
        }

        // Additional protection: Never risk more than 25% of total winnings
        double maxWinningsRisk = g_currentWinStreak * 0.25;
        if(actualLossAmount > maxWinningsRisk)
        {
            double adjustedLossAmount = maxWinningsRisk;
            // Recalculate loss points based on adjusted target
            lossPoints = adjustedLossAmount / pointValueForThisLot;
            actualLossAmount = adjustedLossAmount;
            LogMessage(StringFormat("🛡️ WINNINGS PROTECTION: SL limited to $%.2f (25%% of $%.2f total winnings)",
                      adjustedLossAmount, g_currentWinStreak));
        }
    }

    LogMessage(StringFormat("Target: $%.2f profit, $%.2f loss (Step %d)",
              targetProfitAmount, actualLossAmount, stepIndex + 1));

    // Apply minimum distances for broker compliance - ENHANCED for BTC
    double minStopLevel = SymbolInfoInteger(Symbol(), SYMBOL_TRADE_STOPS_LEVEL) * point;
    double minPoints = minStopLevel / point;

    // CRITICAL: Set realistic minimums for M1 scalping while maintaining broker compliance
    if(StringFind(symbol, "BTC") >= 0)
    {
        minPoints = MathMax(minPoints, 800); // OPTIMIZED: Reduced from 2000 to 800 for M1 scalping
    }
    else if(StringFind(symbol, "XAU") >= 0)
    {
        minPoints = MathMax(minPoints, 60); // OPTIMIZED: Reduced from 200 to 60 for M1 scalping
    }
    else
    {
        minPoints = MathMax(minPoints, 80); // OPTIMIZED: Reduced from 300 to 80 for M1 scalping
    }

    LogMessage(StringFormat("M1 BROKER COMPLIANCE: Symbol=%s, MinStopLevel=%.0f, Applied=%.0f",
              symbol, minStopLevel/point, minPoints));

    // Ensure our distances meet broker requirements (our distances should already be larger)
    profitPoints = MathMax(profitPoints, minPoints * 1.2); // 1.2x safety margin
    lossPoints = MathMax(lossPoints, minPoints * 1.1);     // 1.1x safety margin



    // Calculate actual SL and TP prices
    double profitDistance = profitPoints * point;
    double lossDistance = lossPoints * point;

    if(orderType == ORDER_TYPE_BUY)
    {
        tp = price + profitDistance;
        sl = price - lossDistance;
    }
    else
    {
        tp = price - profitDistance;
        sl = price + lossDistance;
    }

    // Normalize prices
    sl = NormalizeDouble(sl, digits);
    tp = NormalizeDouble(tp, digits);

    // Final broker compliance check
    double currentPrice = (orderType == ORDER_TYPE_BUY) ?
                         SymbolInfoDouble(Symbol(), SYMBOL_ASK) :
                         SymbolInfoDouble(Symbol(), SYMBOL_BID);

    if(orderType == ORDER_TYPE_BUY)
    {
        if(sl >= currentPrice - minStopLevel)
        {
            sl = currentPrice - minStopLevel * 2;
            LogMessage(StringFormat("SL adjusted for broker compliance: %.3f", sl));
        }
        if(tp <= currentPrice + minStopLevel)
        {
            tp = currentPrice + minStopLevel * 2;
            LogMessage(StringFormat("TP adjusted for broker compliance: %.3f", tp));
        }
    }
    else
    {
        if(sl <= currentPrice + minStopLevel)
        {
            sl = currentPrice + minStopLevel * 2;
            LogMessage(StringFormat("SL adjusted for broker compliance: %.3f", sl));
        }
        if(tp >= currentPrice - minStopLevel)
        {
            tp = currentPrice - minStopLevel * 2;
            LogMessage(StringFormat("TP adjusted for broker compliance: %.3f", tp));
        }
    }

    // Calculate actual profit/loss amounts for verification
    double actualProfitPoints = MathAbs(tp - price) / point;
    double actualLossPoints = MathAbs(price - sl) / point;
    double finalProfitAmount = actualProfitPoints * pointValueForThisLot;
    double finalLossAmount = actualLossPoints * pointValueForThisLot;
    double riskRewardRatio = (actualLossPoints > 0) ? actualProfitPoints / actualLossPoints : 0;

    LogMessage(StringFormat("SL/TP: %.3f/%.3f (R:R=1:%.2f)", sl, tp, riskRewardRatio));

    // Final validation check
    if(MathAbs(finalProfitAmount - targetProfitAmount) > 1.0)
    {
        LogMessage(StringFormat("WARNING: Profit amount mismatch! Actual=$%.2f, Target=$%.2f",
                  finalProfitAmount, targetProfitAmount));
    }
    if(MathAbs(finalLossAmount - targetLossAmount) > 1.0)
    {
        LogMessage(StringFormat("WARNING: Loss amount mismatch! Actual=$%.2f, Target=$%.2f",
                  finalLossAmount, targetLossAmount));
    }
}

//+------------------------------------------------------------------+
//| CRITICAL: Calculate spread cost for M1 scalping awareness       |
//+------------------------------------------------------------------+
double GetSpreadCost()
{
    double spread = SymbolInfoInteger(Symbol(), SYMBOL_SPREAD) * SymbolInfoDouble(Symbol(), SYMBOL_POINT);
    LogMessage(StringFormat("DEBUG: Current spread = %.5f", spread));
    return spread;
}

//+------------------------------------------------------------------+
//| CRITICAL: Calculate spread-aware profit target                   |
//+------------------------------------------------------------------+
double CalculateSpreadAwareProfitTarget(double baseTarget)
{
    double spreadCost = GetSpreadCost();
    double spreadBuffer = spreadCost * 2.0; // Entry + Exit spread cost
    double bufferMultiplier = 1.5; // 50% additional buffer for safety

    double adjustedTarget = baseTarget + (spreadBuffer * bufferMultiplier);

    LogMessage(StringFormat("SPREAD AWARENESS: Base=$%.2f, Spread=%.5f, Buffer=$%.2f, Final=$%.2f",
              baseTarget, spreadCost, spreadBuffer * bufferMultiplier, adjustedTarget));

    return adjustedTarget;
}

//+------------------------------------------------------------------+
//| Get point value per lot for current symbol                       |
//+------------------------------------------------------------------+
double GetPointValuePerLot()
{
    string symbol = Symbol();

    // Get actual broker values first
    double tickValue = SymbolInfoDouble(symbol, SYMBOL_TRADE_TICK_VALUE);
    double tickSize = SymbolInfoDouble(symbol, SYMBOL_TRADE_TICK_SIZE);
    double point = SymbolInfoDouble(symbol, SYMBOL_POINT);

    LogMessage(StringFormat("DEBUG: Symbol=%s, TickValue=%.4f, TickSize=%.6f, Point=%.6f",
              symbol, tickValue, tickSize, point));

    // For BTC, the point value is much higher due to the price level
    if(StringFind(symbol, "BTC") >= 0)
    {
        // BTC trades around 100,000+ so 1 point (0.01) movement = $0.01 per lot
        // But for realistic profit targets, we need to use actual tick value
        double btcPointValue = tickValue; // Use actual tick value from broker
        LogMessage(StringFormat("DEBUG: BTC point value = %.6f (using tick value)", btcPointValue));
        return btcPointValue;
    }

    // Calculate actual point value for other instruments
    if(tickSize > 0 && point > 0 && tickValue > 0)
    {
        double actualPointValue = (tickValue * point) / tickSize;
        LogMessage(StringFormat("DEBUG: Calculated point value = %.6f", actualPointValue));
        return actualPointValue;
    }

    // Fallback values based on instrument type
    if(StringFind(symbol, "XAU") >= 0 || StringFind(symbol, "GOLD") >= 0)
    {
        LogMessage("DEBUG: Using XAU fallback point value = 0.10");
        return 0.10; // XAU/USD: $0.10 per point per lot
    }
    else
    {
        LogMessage("DEBUG: Using default fallback point value = 0.10");
        return 0.10; // Default for other USD pairs
    }
}

//+------------------------------------------------------------------+
//| Test Anti-Martingale progression (for debugging)                 |
//+------------------------------------------------------------------+
void TestAntiMartingaleProgression()
{
    LogMessage("🧪 TESTING ANTI-MARTINGALE PROGRESSION:");
    LogMessage(StringFormat("📊 Win Sequence Array: [%.2f, %.2f, %.2f, %.2f, %.2f]",
              g_winSequence[0], g_winSequence[1], g_winSequence[2], g_winSequence[3], g_winSequence[4]));
    LogMessage(StringFormat("📊 Initial Lot: %.3f, Max Steps: %d", InitialLot, g_configuredMaxAntiMartingaleStep));

    // Reset counters
    g_consecutiveWins = 0;
    g_consecutiveLosses = 0;
    g_currentWinStreak = 0;
    g_currentStep = 0;
    g_isInWinningStreak = false;

    // Simulate 5 consecutive wins
    for(int i = 0; i < 5; i++)
    {
        LogMessage(StringFormat("--- SIMULATING WIN #%d ---", i + 1));

        // Calculate lot size before win
        double lotBefore = CalculateLotSize(0.5); // Medium signal strength
        LogMessage(StringFormat("Lot size BEFORE win: %.3f", lotBefore));

        // Register the win
        UpdateAntiMartingaleCounters(true, 5.0); // $5 profit

        // Calculate lot size after win
        double lotAfter = CalculateLotSize(0.5);
        LogMessage(StringFormat("Lot size AFTER win: %.3f", lotAfter));
        LogMessage("");
    }

    LogMessage("🧪 ANTI-MARTINGALE TEST COMPLETED");
}

//+------------------------------------------------------------------+
//| CRITICAL: Verify M1 scalping optimizations are working          |
//+------------------------------------------------------------------+
void VerifyM1ScalpingOptimizations()
{
    LogMessage("🔍 VERIFYING M1 SCALPING OPTIMIZATIONS:");

    // Test signal thresholds
    LogMessage(StringFormat("✅ Signal Thresholds: MinTrendStrength=%.3f, MinConfluence=%d, ADX=%s",
              g_configuredMinTrendStrength, g_configuredMinConfluenceSignals,
              g_configuredRequireADXConfirmation ? "ENABLED" : "DISABLED"));

    // Test profit target calculations
    string symbol = Symbol();
    double baseTarget = 3.0;
    if(StringFind(symbol, "BTC") >= 0) baseTarget = 5.0;
    else if(StringFind(symbol, "XAU") >= 0) baseTarget = 4.0;

    double rawTarget = baseTarget * g_profitSteps[0]; // Step 1
    double spreadAwareTarget = CalculateSpreadAwareProfitTarget(rawTarget);

    LogMessage(StringFormat("✅ Profit Targets: Base=$%.2f, Raw=$%.2f, Spread-Aware=$%.2f",
              baseTarget, rawTarget, spreadAwareTarget));

    // Test point distance calculations
    double testLotSize = InitialLot;
    double pointValue = GetPointValuePerLot();
    double pointValueForLot = pointValue * testLotSize;

    double requiredPoints = spreadAwareTarget / pointValueForLot;
    double finalPoints = 0;

    if(StringFind(symbol, "BTC") >= 0)
    {
        finalPoints = MathMax(requiredPoints, 1000); // New minimum
    }
    else if(StringFind(symbol, "XAU") >= 0)
    {
        finalPoints = MathMax(requiredPoints, 80); // New minimum
    }
    else
    {
        finalPoints = MathMax(requiredPoints, 100); // New minimum
    }

    double actualProfitAmount = finalPoints * pointValueForLot;

    LogMessage(StringFormat("✅ Point Distances: Required=%.0f, Final=%.0f, Actual Profit=$%.2f",
              requiredPoints, finalPoints, actualProfitAmount));

    // Verify realistic ranges
    bool isRealistic = true;
    if(StringFind(symbol, "BTC") >= 0 && finalPoints > 2000)
    {
        LogMessage("⚠️ WARNING: BTC points still too high for M1 scalping!");
        isRealistic = false;
    }
    else if(StringFind(symbol, "XAU") >= 0 && finalPoints > 200)
    {
        LogMessage("⚠️ WARNING: XAU points still too high for M1 scalping!");
        isRealistic = false;
    }
    else if(finalPoints > 300)
    {
        LogMessage("⚠️ WARNING: Default points still too high for M1 scalping!");
        isRealistic = false;
    }

    if(isRealistic)
    {
        LogMessage("✅ M1 SCALPING VERIFICATION: All optimizations working correctly!");
    }
    else
    {
        LogMessage("❌ M1 SCALPING VERIFICATION: Some optimizations need adjustment!");
    }

    LogMessage("🔍 M1 SCALPING VERIFICATION COMPLETED");
}

//+------------------------------------------------------------------+
//| Initialize Asymmetric Risk-Reward System                        |
//+------------------------------------------------------------------+
void InitializeAsymmetricSystem()
{
    if(!g_useAsymmetricRR)
    {
        LogMessage("ASYMMETRIC SYSTEM: Disabled - using traditional system");
        return;
    }

    // Validate account balance requirement
    double currentBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    if(currentBalance < g_asymmetricRisk.minAccountBalance)
    {
        LogMessage(StringFormat("WARNING: Account balance $%.2f below minimum $%.2f for asymmetric system",
                  currentBalance, g_asymmetricRisk.minAccountBalance));
        LogMessage("ASYMMETRIC SYSTEM: Disabled due to insufficient balance");
        g_useAsymmetricRR = false;
        g_asymmetricRisk.asymmetricSystemEnabled = false;
        return;
    }

    // Update progression arrays for asymmetric system
    ArrayCopy(g_winSequence, g_asymmetricWinSequence);
    ArrayCopy(g_profitSteps, g_asymmetricProfitSteps);
    ArrayCopy(g_lossSteps, g_asymmetricLossSteps);

    // Initialize risk control parameters
    g_asymmetricRisk.consecutiveAsymmetricLosses = 0;
    g_asymmetricRisk.totalAsymmetricRisk = 0.0;
    g_asymmetricRisk.lastAsymmetricTrade = 0;
    g_asymmetricRisk.asymmetricSystemEnabled = true;

    LogMessage("=== ASYMMETRIC 1:3 RISK-REWARD SYSTEM INITIALIZED ===");
    LogMessage(StringFormat("Profit Target: $%.2f | Stop Loss: $%.2f | Ratio: 1:%.1f",
              g_asymmetricProfitTarget, g_asymmetricSLTarget,
              g_asymmetricSLTarget / g_asymmetricProfitTarget));
    LogMessage(StringFormat("Risk Controls: Max Risk/Trade: %.1f%% | Max Exposure: %.1f%% | Max Daily Trades: %d",
              g_asymmetricRisk.maxRiskPerTrade * 100, g_asymmetricRisk.maxTotalExposure * 100,
              g_asymmetricRisk.maxDailyTrades));
    LogMessage(StringFormat("Progression: [%.1f, %.1f, %.1f, %.1f, %.1f, %.1f]",
              g_asymmetricWinSequence[0], g_asymmetricWinSequence[1], g_asymmetricWinSequence[2],
              g_asymmetricWinSequence[3], g_asymmetricWinSequence[4], g_asymmetricWinSequence[5]));
}

//+------------------------------------------------------------------+
//| Validate Asymmetric Risk Before Trade Execution                 |
//+------------------------------------------------------------------+
bool ValidateAsymmetricRisk(double lotSize, double slAmount)
{
    if(!g_useAsymmetricRR || !g_asymmetricRisk.asymmetricSystemEnabled)
        return true; // Skip validation if asymmetric system disabled

    double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);

    // Check minimum account balance
    if(accountBalance < g_asymmetricRisk.minAccountBalance)
    {
        LogMessage(StringFormat("RISK VALIDATION FAILED: Account balance $%.2f below minimum $%.2f",
                  accountBalance, g_asymmetricRisk.minAccountBalance));
        return false;
    }

    // Calculate risk per trade
    double riskAmount = slAmount * lotSize;
    double riskPercentage = riskAmount / accountBalance;

    if(riskPercentage > g_asymmetricRisk.maxRiskPerTrade)
    {
        LogMessage(StringFormat("RISK VALIDATION FAILED: Risk per trade %.2f%% exceeds maximum %.2f%%",
                  riskPercentage * 100, g_asymmetricRisk.maxRiskPerTrade * 100));
        LogMessage(StringFormat("Risk Details: Amount=$%.2f, Lot=%.3f, SL=$%.2f, Balance=$%.2f",
                  riskAmount, lotSize, slAmount, accountBalance));
        return false;
    }

    // Check total exposure
    double currentExposure = CalculateCurrentAsymmetricExposure();
    double totalExposure = (currentExposure + riskAmount) / accountBalance;

    if(totalExposure > g_asymmetricRisk.maxTotalExposure)
    {
        LogMessage(StringFormat("RISK VALIDATION FAILED: Total exposure %.2f%% exceeds maximum %.2f%%",
                  totalExposure * 100, g_asymmetricRisk.maxTotalExposure * 100));
        LogMessage(StringFormat("Exposure Details: Current=$%.2f, New=$%.2f, Total=$%.2f",
                  currentExposure, riskAmount, currentExposure + riskAmount));
        return false;
    }

    // Check consecutive losses circuit breaker
    if(g_asymmetricRisk.consecutiveAsymmetricLosses >= 3)
    {
        LogMessage(StringFormat("RISK VALIDATION FAILED: Circuit breaker activated - %d consecutive losses",
                  g_asymmetricRisk.consecutiveAsymmetricLosses));
        return false;
    }

    // Check daily trade limit
    datetime currentTime = TimeCurrent();
    MqlDateTime timeStruct;
    TimeToStruct(currentTime, timeStruct);

    MqlDateTime lastTradeStruct;
    TimeToStruct(g_asymmetricRisk.lastAsymmetricTrade, lastTradeStruct);

    if(timeStruct.day == lastTradeStruct.day)
    {
        // Same day - check trade count (simplified check)
        int dailyTrades = CountDailyAsymmetricTrades();
        if(dailyTrades >= g_asymmetricRisk.maxDailyTrades)
        {
            LogMessage(StringFormat("RISK VALIDATION FAILED: Daily trade limit reached (%d/%d)",
                      dailyTrades, g_asymmetricRisk.maxDailyTrades));
            return false;
        }
    }

    LogMessage(StringFormat("RISK VALIDATION PASSED: Risk=%.2f%% (%.2f), Exposure=%.2f%%, Losses=%d",
              riskPercentage * 100, riskAmount, totalExposure * 100,
              g_asymmetricRisk.consecutiveAsymmetricLosses));

    return true;
}

//+------------------------------------------------------------------+
//| Calculate Current Asymmetric Exposure                           |
//+------------------------------------------------------------------+
double CalculateCurrentAsymmetricExposure()
{
    double totalExposure = 0.0;

    for(int i = 0; i < ArraySize(g_orders); i++)
    {
        if(PositionSelectByTicket(g_orders[i].ticket))
        {
            double positionSL = PositionGetDouble(POSITION_SL);
            double positionPrice = PositionGetDouble(POSITION_PRICE_OPEN);
            double positionLots = PositionGetDouble(POSITION_VOLUME);

            // Calculate potential loss
            double pointValue = GetPointValuePerLot();
            double slDistance = MathAbs(positionPrice - positionSL);
            double potentialLoss = (slDistance / SymbolInfoDouble(Symbol(), SYMBOL_POINT)) * pointValue * positionLots;

            totalExposure += potentialLoss;
        }
    }

    return totalExposure;
}

//+------------------------------------------------------------------+
//| Count Daily Asymmetric Trades                                   |
//+------------------------------------------------------------------+
int CountDailyAsymmetricTrades()
{
    // Simplified implementation - count recent trades in history
    // In production, this should track trades more precisely
    datetime currentTime = TimeCurrent();
    datetime dayStart = currentTime - (currentTime % 86400); // Start of current day

    int dailyTrades = 0;

    // Check recent trade history (last 10 trades)
    for(int i = 0; i < 10; i++)
    {
        if(g_recentTrades[i].closeTime >= dayStart && g_recentTrades[i].closeTime > 0)
        {
            dailyTrades++;
        }
    }

    return dailyTrades;
}

//+------------------------------------------------------------------+
//| Calculate Asymmetric Stop Levels                                |
//+------------------------------------------------------------------+
void CalculateAsymmetricStopLevels(int orderType, double price, double &sl, double &tp, double lotSize, int chainId = -1)
{
    if(!g_useAsymmetricRR || !g_asymmetricRisk.asymmetricSystemEnabled)
    {
        // Fall back to original calculation
        CalculateStopLevels(orderType, price, sl, tp, lotSize, chainId);
        return;
    }

    // Get current Anti-Martingale step
    int stepIndex = 0;
    if(chainId > 0 && chainId <= 3)
    {
        int chainIndex = chainId - 1;
        stepIndex = g_orderChains[chainIndex].currentStep;
    }
    else
    {
        stepIndex = g_currentStep;
    }
    stepIndex = MathMin(stepIndex, ArraySize(g_asymmetricProfitSteps) - 1);

    // Calculate asymmetric targets with progression
    double baseProfit = g_asymmetricProfitTarget;
    double baseSL = g_asymmetricSLTarget;

    // Apply progression multipliers
    double targetProfitAmount = baseProfit * g_asymmetricProfitSteps[stepIndex];
    double targetSLAmount = baseSL * g_asymmetricLossSteps[stepIndex];

    // Apply anti-martingale protection for SL
    targetSLAmount = ApplyAsymmetricAntiMartingaleProtection(targetSLAmount, chainId);

    // Validate risk before proceeding
    if(!ValidateAsymmetricRisk(lotSize, targetSLAmount))
    {
        LogMessage("ASYMMETRIC TRADE REJECTED: Risk validation failed");
        sl = 0;
        tp = 0;
        return;
    }

    // Calculate point distances
    double pointValueForThisLot = GetPointValuePerLot() * lotSize;
    double profitPoints = targetProfitAmount / pointValueForThisLot;
    double lossPoints = targetSLAmount / pointValueForThisLot;

    // Apply minimum broker requirements
    string symbol = Symbol();
    double minStopLevel = SymbolInfoInteger(symbol, SYMBOL_TRADE_STOPS_LEVEL) * SymbolInfoDouble(symbol, SYMBOL_POINT);
    double minPoints = minStopLevel / SymbolInfoDouble(symbol, SYMBOL_POINT);

    // Set realistic minimums for asymmetric system
    if(StringFind(symbol, "BTC") >= 0)
    {
        minPoints = MathMax(minPoints, 1200); // Higher minimum for BTC with wider stops
    }
    else if(StringFind(symbol, "XAU") >= 0)
    {
        minPoints = MathMax(minPoints, 100); // Higher minimum for XAU
    }
    else
    {
        minPoints = MathMax(minPoints, 120); // Higher minimum for other instruments
    }

    profitPoints = MathMax(profitPoints, minPoints * 1.2);
    lossPoints = MathMax(lossPoints, minPoints * 1.1);

    // Calculate final prices
    double point = SymbolInfoDouble(symbol, SYMBOL_POINT);
    if(orderType == ORDER_TYPE_BUY)
    {
        tp = price + (profitPoints * point);
        sl = price - (lossPoints * point);
    }
    else
    {
        tp = price - (profitPoints * point);
        sl = price + (lossPoints * point);
    }

    // Normalize prices
    int digits = (int)SymbolInfoInteger(symbol, SYMBOL_DIGITS);
    sl = NormalizeDouble(sl, digits);
    tp = NormalizeDouble(tp, digits);

    // Final validation and logging
    double actualProfitAmount = MathAbs(tp - price) / point * pointValueForThisLot;
    double actualLossAmount = MathAbs(price - sl) / point * pointValueForThisLot;
    double actualRR = (actualLossAmount > 0) ? actualProfitAmount / actualLossAmount : 0;

    LogMessage(StringFormat("ASYMMETRIC SL/TP: Step %d, Profit=$%.2f, SL=$%.2f, R:R=1:%.2f",
              stepIndex + 1, actualProfitAmount, actualLossAmount, actualRR));
    LogMessage(StringFormat("ASYMMETRIC PRICES: Entry=%.5f, TP=%.5f, SL=%.5f",
              price, tp, sl));
}

//+------------------------------------------------------------------+
//| Apply Asymmetric Anti-Martingale Protection                     |
//+------------------------------------------------------------------+
double ApplyAsymmetricAntiMartingaleProtection(double targetSLAmount, int chainId = -1)
{
    double protectedSL = targetSLAmount;

    // Get consecutive wins and last trade profit
    int consecutiveWins = 0;
    double lastTradeProfit = 0.0;
    double winStreak = 0.0;

    if(chainId > 0 && chainId <= 3)
    {
        int chainIndex = chainId - 1;
        consecutiveWins = g_orderChains[chainIndex].consecutiveWins;
        lastTradeProfit = g_orderChains[chainIndex].lastTradeProfit;
        winStreak = g_orderChains[chainIndex].winStreak;
    }
    else
    {
        consecutiveWins = g_consecutiveWins;
        winStreak = g_currentWinStreak;

        // Get last trade profit from history
        if(g_tradeHistoryIndex > 0)
        {
            int lastIndex = (g_tradeHistoryIndex - 1) % 10;
            if(g_recentTrades[lastIndex].isWin)
            {
                lastTradeProfit = g_recentTrades[lastIndex].profit;
            }
        }
    }

    // Apply graduated protection based on consecutive wins
    if(consecutiveWins == 0)
    {
        // First trade: Allow full asymmetric SL
        protectedSL = targetSLAmount;
        LogMessage(StringFormat("ANTI-MARTINGALE: First trade - Full SL allowed: $%.2f", protectedSL));
    }
    else if(consecutiveWins == 1)
    {
        // Second trade: Allow up to 1.5x previous profit
        double maxAllowedSL = lastTradeProfit * 1.5;
        protectedSL = MathMin(targetSLAmount, maxAllowedSL);
        LogMessage(StringFormat("ANTI-MARTINGALE: 2nd trade - SL limited to 1.5x last profit: $%.2f (was $%.2f)",
                  protectedSL, targetSLAmount));
    }
    else if(consecutiveWins >= 2)
    {
        // Third+ trade: Strict anti-martingale protection
        if(consecutiveWins >= 3)
        {
            // After 3+ wins, limit SL to 25% of total win streak
            double streakProtection = winStreak * 0.25;
            protectedSL = MathMin(targetSLAmount, streakProtection);
            LogMessage(StringFormat("ANTI-MARTINGALE: %d wins - SL limited to 25%% of streak: $%.2f (streak: $%.2f)",
                      consecutiveWins, protectedSL, winStreak));
        }
        else
        {
            // 2-3 wins: Use 95% of last profit rule
            double lastProfitProtection = lastTradeProfit * 0.95;
            protectedSL = MathMin(targetSLAmount, lastProfitProtection);
            LogMessage(StringFormat("ANTI-MARTINGALE: %d wins - SL limited to 95%% of last profit: $%.2f (last: $%.2f)",
                      consecutiveWins, protectedSL, lastTradeProfit));
        }
    }

    // Ensure minimum viable SL (at least $3 for asymmetric system)
    double minViableSL = 3.0;
    if(protectedSL < minViableSL)
    {
        LogMessage(StringFormat("ANTI-MARTINGALE: SL too small (%.2f), using minimum viable SL: $%.2f",
                  protectedSL, minViableSL));
        protectedSL = minViableSL;
    }

    return protectedSL;
}

//+------------------------------------------------------------------+
//| Initialize Time-Based Management for New Order                  |
//+------------------------------------------------------------------+
void InitializeTimeBasedManagement(int orderIndex)
{
    if(orderIndex < 0 || orderIndex >= ArraySize(g_orders))
        return;

    datetime currentTime = TimeCurrent();

    // Initialize time-based management structure
    g_orders[orderIndex].timeManagement.openTime = currentTime;
    g_orders[orderIndex].timeManagement.fiveMinuteMark = currentTime + (5 * 60);
    g_orders[orderIndex].timeManagement.tenMinuteMark = currentTime + (10 * 60);
    g_orders[orderIndex].timeManagement.fifteenMinuteMark = currentTime + (15 * 60);
    g_orders[orderIndex].timeManagement.maxCloseTime = currentTime + (g_timeBasedConfig.maxPositionDuration * 60);

    g_orders[orderIndex].timeManagement.fiveMinuteChecked = false;
    g_orders[orderIndex].timeManagement.tenMinuteChecked = false;
    g_orders[orderIndex].timeManagement.fifteenMinuteChecked = false;

    g_orders[orderIndex].timeManagement.initialProfit = 0.0;
    g_orders[orderIndex].timeManagement.fiveMinuteProfit = 0.0;
    g_orders[orderIndex].timeManagement.tenMinuteProfit = 0.0;
    g_orders[orderIndex].timeManagement.fifteenMinuteProfit = 0.0;

    g_orders[orderIndex].timeManagement.momentumReversalDetected = false;
    g_orders[orderIndex].timeManagement.lastMomentumCheck = currentTime;

    LogMessage(StringFormat("TIME MANAGEMENT INITIALIZED: Order %d, Duration: %d min, Checkpoints: 5/10/15 min",
              g_orders[orderIndex].ticket, g_timeBasedConfig.maxPositionDuration));
}

//+------------------------------------------------------------------+
//| Apply Time-Based Position Management                            |
//+------------------------------------------------------------------+
bool ApplyTimeBasedManagement(int orderIndex)
{
    if(!g_timeBasedConfig.enableTimeBasedManagement)
        return false; // Time-based management disabled

    if(orderIndex < 0 || orderIndex >= ArraySize(g_orders))
        return false;

    if(!PositionSelectByTicket(g_orders[orderIndex].ticket))
        return false; // Position no longer exists

    datetime currentTime = TimeCurrent();
    double currentProfit = PositionGetDouble(POSITION_PROFIT);
    TimeBasedManagement &tbm = g_orders[orderIndex].timeManagement;

    // 5-minute checkpoint
    if(currentTime >= tbm.fiveMinuteMark && !tbm.fiveMinuteChecked)
    {
        tbm.fiveMinuteChecked = true;
        tbm.fiveMinuteProfit = currentProfit;

        if(currentProfit < g_timeBasedConfig.fiveMinuteThreshold)
        {
            LogMessage(StringFormat("5-MIN EXIT: Order %d profit $%.2f < threshold $%.2f",
                      g_orders[orderIndex].ticket, currentProfit, g_timeBasedConfig.fiveMinuteThreshold));
            return ClosePositionWithReason(orderIndex, "5MIN_INSUFFICIENT_PROGRESS");
        }
        else
        {
            LogMessage(StringFormat("5-MIN CHECK PASSED: Order %d profit $%.2f >= threshold $%.2f",
                      g_orders[orderIndex].ticket, currentProfit, g_timeBasedConfig.fiveMinuteThreshold));
        }
    }

    // 10-minute checkpoint
    if(currentTime >= tbm.tenMinuteMark && !tbm.tenMinuteChecked)
    {
        tbm.tenMinuteChecked = true;
        tbm.tenMinuteProfit = currentProfit;

        if(currentProfit < g_timeBasedConfig.tenMinuteThreshold)
        {
            LogMessage(StringFormat("10-MIN EXIT: Order %d profit $%.2f < threshold $%.2f",
                      g_orders[orderIndex].ticket, currentProfit, g_timeBasedConfig.tenMinuteThreshold));
            return ClosePositionWithReason(orderIndex, "10MIN_SLOW_PROGRESS");
        }
        else
        {
            LogMessage(StringFormat("10-MIN CHECK PASSED: Order %d profit $%.2f >= threshold $%.2f",
                      g_orders[orderIndex].ticket, currentProfit, g_timeBasedConfig.tenMinuteThreshold));
        }
    }

    // 15-minute checkpoint
    if(currentTime >= tbm.fifteenMinuteMark && !tbm.fifteenMinuteChecked)
    {
        tbm.fifteenMinuteChecked = true;
        tbm.fifteenMinuteProfit = currentProfit;

        if(currentProfit < g_timeBasedConfig.fifteenMinuteThreshold)
        {
            LogMessage(StringFormat("15-MIN EXIT: Order %d profit $%.2f < threshold $%.2f",
                      g_orders[orderIndex].ticket, currentProfit, g_timeBasedConfig.fifteenMinuteThreshold));
            return ClosePositionWithReason(orderIndex, "15MIN_TARGET_NOT_REACHED");
        }
        else
        {
            LogMessage(StringFormat("15-MIN CHECK PASSED: Order %d profit $%.2f >= threshold $%.2f",
                      g_orders[orderIndex].ticket, currentProfit, g_timeBasedConfig.fifteenMinuteThreshold));
        }
    }

    // Maximum duration check (20 minutes)
    if(currentTime >= tbm.maxCloseTime)
    {
        LogMessage(StringFormat("MAX DURATION EXIT: Order %d reached %d-minute limit, profit: $%.2f",
                  g_orders[orderIndex].ticket, g_timeBasedConfig.maxPositionDuration, currentProfit));
        return ClosePositionWithReason(orderIndex, "MAX_DURATION_TIMEOUT");
    }

    // Momentum reversal check
    if(currentTime - tbm.lastMomentumCheck >= g_timeBasedConfig.momentumCheckInterval)
    {
        if(DetectMomentumReversal(orderIndex))
        {
            LogMessage(StringFormat("MOMENTUM REVERSAL EXIT: Order %d momentum reversed, profit: $%.2f",
                      g_orders[orderIndex].ticket, currentProfit));
            return ClosePositionWithReason(orderIndex, "MOMENTUM_REVERSAL");
        }
        tbm.lastMomentumCheck = currentTime;
    }

    return false; // No exit triggered
}

//+------------------------------------------------------------------+
//| Detect Momentum Reversal for Early Exit                        |
//+------------------------------------------------------------------+
bool DetectMomentumReversal(int orderIndex)
{
    if(orderIndex < 0 || orderIndex >= ArraySize(g_orders))
        return false;

    // Get position information
    if(!PositionSelectByTicket(g_orders[orderIndex].ticket))
        return false;

    int positionType = (int)PositionGetInteger(POSITION_TYPE);
    double currentPrice = (positionType == POSITION_TYPE_BUY) ?
                         SymbolInfoDouble(Symbol(), SYMBOL_BID) :
                         SymbolInfoDouble(Symbol(), SYMBOL_ASK);

    // Calculate momentum indicators
    double priceChange = CalculateRecentPriceChange(5); // 5-period price change
    double rsiValue = GetCurrentRSI();
    double macdValue = GetCurrentMACD();

    // Determine if momentum is reversing against position
    bool momentumReversing = false;

    if(positionType == POSITION_TYPE_BUY)
    {
        // For BUY positions, check for bearish momentum reversal
        if(priceChange < -g_timeBasedConfig.momentumReversalThreshold ||
           rsiValue > 70 ||
           macdValue < 0)
        {
            momentumReversing = true;
        }
    }
    else
    {
        // For SELL positions, check for bullish momentum reversal
        if(priceChange > g_timeBasedConfig.momentumReversalThreshold ||
           rsiValue < 30 ||
           macdValue > 0)
        {
            momentumReversing = true;
        }
    }

    if(momentumReversing)
    {
        g_orders[orderIndex].timeManagement.momentumReversalDetected = true;
        LogMessage(StringFormat("MOMENTUM REVERSAL DETECTED: Order %d, Type: %s, PriceChange: %.4f, RSI: %.1f, MACD: %.4f",
                  g_orders[orderIndex].ticket,
                  (positionType == POSITION_TYPE_BUY) ? "BUY" : "SELL",
                  priceChange, rsiValue, macdValue));
    }

    return momentumReversing;
}

//+------------------------------------------------------------------+
//| Close Position with Specific Reason                             |
//+------------------------------------------------------------------+
bool ClosePositionWithReason(int orderIndex, string reason)
{
    if(orderIndex < 0 || orderIndex >= ArraySize(g_orders))
        return false;

    ulong ticket = g_orders[orderIndex].ticket;

    if(!PositionSelectByTicket(ticket))
        return false;

    double currentProfit = PositionGetDouble(POSITION_PROFIT);

    CTrade trade;
    if(trade.PositionClose(ticket))
    {
        LogMessage(StringFormat("✅ POSITION CLOSED: Ticket %d, Reason: %s, Profit: $%.2f",
                  ticket, reason, currentProfit));

        // Update tracking before removal
        UpdateTradeTracking(orderIndex);
        RemoveOrderFromTracking(orderIndex);

        return true;
    }
    else
    {
        LogMessage(StringFormat("❌ FAILED TO CLOSE: Ticket %d, Reason: %s, Error: %s",
                  ticket, reason, trade.ResultRetcodeDescription()));
        return false;
    }
}

//+------------------------------------------------------------------+
//| Calculate Recent Price Change                                   |
//+------------------------------------------------------------------+
double CalculateRecentPriceChange(int periods)
{
    double close[];
    ArraySetAsSeries(close, true);

    if(CopyClose(Symbol(), PERIOD_M1, 0, periods + 1, close) < periods + 1)
        return 0.0;

    if(close[periods] <= 0)
        return 0.0;

    return (close[0] - close[periods]) / close[periods];
}

//+------------------------------------------------------------------+
//| Get Current RSI Value                                           |
//+------------------------------------------------------------------+
double GetCurrentRSI()
{
    if(g_rsiHandle == INVALID_HANDLE)
        return 50.0; // Neutral value if RSI not available

    double rsi[];
    ArraySetAsSeries(rsi, true);

    if(CopyBuffer(g_rsiHandle, 0, 0, 1, rsi) < 1)
        return 50.0;

    return rsi[0];
}

//+------------------------------------------------------------------+
//| Get Current MACD Value                                          |
//+------------------------------------------------------------------+
double GetCurrentMACD()
{
    if(g_macdHandle == INVALID_HANDLE)
        return 0.0; // Neutral value if MACD not available

    double macd[];
    ArraySetAsSeries(macd, true);

    if(CopyBuffer(g_macdHandle, 0, 0, 1, macd) < 1)
        return 0.0;

    return macd[0];
}

//+------------------------------------------------------------------+
//| Add order to tracking system with trailing profit initialization |
//+------------------------------------------------------------------+
void AddOrderToTracking(ulong ticket, int type, double lots, double openPrice, double tp, double sl, string comment, double signalStrength, int chainId = -1)
{
    // Resize array if needed
    int newSize = ArraySize(g_orders) + 1;
    ArrayResize(g_orders, newSize);

    int index = newSize - 1;

    // Initialize order tracking data
    g_orders[index].ticket = ticket;
    g_orders[index].type = type;
    g_orders[index].lots = lots;
    g_orders[index].openPrice = openPrice;
    g_orders[index].targetProfit = tp;
    g_orders[index].stopLoss = sl;
    g_orders[index].openTime = TimeCurrent();
    g_orders[index].comment = comment;
    g_orders[index].signalStrength = signalStrength;
    g_orders[index].isWinningTrade = false;
    g_orders[index].maxProfit = 0.0;
    g_orders[index].maxLoss = 0.0;

    // Initialize parallel order chain data
    if(chainId > 0 && chainId <= 3)
    {
        int chainIndex = chainId - 1;
        g_orders[index].orderChainId = chainId;
        g_orders[index].orderStep = g_orderChains[chainIndex].currentStep;
        g_orders[index].consecutiveWins = g_orderChains[chainIndex].consecutiveWins;
        g_orders[index].consecutiveLosses = g_orderChains[chainIndex].consecutiveLosses;
        g_orders[index].winStreak = g_orderChains[chainIndex].winStreak;
        g_orders[index].isInWinningStreak = g_orderChains[chainIndex].isInWinningStreak;
        g_orders[index].lastTradeProfit = g_orderChains[chainIndex].lastTradeProfit;
    }
    else
    {
        // Legacy fallback
        g_orders[index].orderChainId = 0;
        g_orders[index].orderStep = g_currentStep;
        g_orders[index].consecutiveWins = g_consecutiveWins;
        g_orders[index].consecutiveLosses = g_consecutiveLosses;
        g_orders[index].winStreak = g_currentWinStreak;
        g_orders[index].isInWinningStreak = g_isInWinningStreak;
        g_orders[index].lastTradeProfit = 0.0;
    }

    // Initialize enhanced time-based management system (replaces 30-minute timeout)
    InitializeTimeBasedManagement(index);

    // Initialize trailing profit system fields
    g_orders[index].trailingProfitActive = false;
    g_orders[index].initialTPTarget = 0.0; // Will be set by InitializeTrailingProfitData
    g_orders[index].trailingTPLevel = tp;
    g_orders[index].maxProfitAchieved = 0.0;
    g_orders[index].lockedProfit = 0.0;
    g_orders[index].lastTrailingUpdate = TimeCurrent();
    g_orders[index].trailingUpdateCount = 0;

    g_orderCount++;
    g_activeOrders++;

    LogMessage(StringFormat("📝 Order added to tracking: Ticket %d, Index %d, Active Orders: %d",
              ticket, index, g_activeOrders));
}

//+------------------------------------------------------------------+
//| Update order tracking and monitor positions                      |
//+------------------------------------------------------------------+
void UpdateOrderTracking()
{
    for(int i = ArraySize(g_orders) - 1; i >= 0; i--)
    {
        // Check if position still exists
        bool positionExists = false;

        // First try to select by ticket
        if(PositionSelectByTicket(g_orders[i].ticket))
        {
            positionExists = true;
        }
        else
        {
            // Double-check by iterating through all positions
            int totalPositions = PositionsTotal();
            for(int j = 0; j < totalPositions; j++)
            {
                if(PositionGetTicket(j) == g_orders[i].ticket)
                {
                    positionExists = true;
                    break;
                }
            }
        }

        if(!positionExists)
        {
            // Position closed - update tracking
            LogMessage(StringFormat("📊 Position closed detected: Ticket %d, Chain %d",
                      g_orders[i].ticket, g_orders[i].orderChainId));

            // Deactivate the chain
            if(g_orders[i].orderChainId > 0)
            {
                DeactivateOrderChain(g_orders[i].orderChainId);
            }

            UpdateTradeTracking(i);
            RemoveOrderFromTracking(i);
        }
        else
        {
            // Check for 30-minute timeout
            datetime currentTime = TimeCurrent();
            if(currentTime >= g_orders[i].maxCloseTime)
            {
                if(!g_orders[i].timeoutWarningShown)
                {
                    LogMessage(StringFormat("⏰ 30-minute timeout reached for Ticket %d, Chain %d - Force closing",
                              g_orders[i].ticket, g_orders[i].orderChainId));
                    g_orders[i].timeoutWarningShown = true;
                }

                // Force close the position
                CTrade trade;
                if(trade.PositionClose(g_orders[i].ticket))
                {
                    LogMessage(StringFormat("✅ Position %d force closed due to timeout", g_orders[i].ticket));
                }
                else
                {
                    LogMessage(StringFormat("❌ Failed to force close position %d: %s",
                              g_orders[i].ticket, trade.ResultRetcodeDescription()));
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Monitor existing positions for profit/loss tracking             |
//+------------------------------------------------------------------+
void MonitorPositions()
{
    for(int i = 0; i < ArraySize(g_orders); i++)
    {
        if(PositionSelectByTicket(g_orders[i].ticket))
        {
            double currentProfit = PositionGetDouble(POSITION_PROFIT);

            // ENHANCED: Check for profit protection closure first
            if(ShouldCloseForProfitProtection(i, currentProfit))
            {
                // Close the order immediately for profit protection
                if(Trade.PositionClose(g_orders[i].ticket))
                {
                    LogMessage(StringFormat("✅ PROFIT PROTECTED: Order %d closed at $%.2f (prevented +$8-9 reversal)",
                              g_orders[i].ticket, currentProfit));

                    // Update tracking before removal
                    UpdateTradeTracking(i);
                    RemoveOrderFromTracking(i);
                    i--; // Adjust index after removal
                    continue;
                }
                else
                {
                    LogMessage(StringFormat("❌ FAILED TO CLOSE PROFIT PROTECTION: Order %d, Error: %d",
                              g_orders[i].ticket, GetLastError()));
                }
            }

            // Update max profit/loss tracking
            if(currentProfit > g_orders[i].maxProfit)
                g_orders[i].maxProfit = currentProfit;
            if(currentProfit < g_orders[i].maxLoss)
                g_orders[i].maxLoss = currentProfit;

            // Update winning trade status
            g_orders[i].isWinningTrade = (currentProfit > 0);

            // Enhanced logging for profit tracking in the +$7-9 zone
            if(currentProfit >= 7.0 && currentProfit <= 10.0)
            {
                LogMessage(StringFormat("🎯 PROFIT ZONE: Order %d at $%.2f profit (monitoring for reversal)",
                          g_orders[i].ticket, currentProfit));
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Update trade tracking when position closes                       |
//+------------------------------------------------------------------+
void UpdateTradeTracking(int orderIndex)
{
    if(orderIndex < 0 || orderIndex >= ArraySize(g_orders))
        return;

    // Get actual final profit from deal history
    double finalProfit = 0.0;
    bool isWin = false;

    // Try to get actual profit from history
    ulong ticket = g_orders[orderIndex].ticket;
    if(HistorySelectByPosition(ticket))
    {
        int dealsTotal = HistoryDealsTotal();
        for(int i = 0; i < dealsTotal; i++)
        {
            ulong dealTicket = HistoryDealGetTicket(i);
            if(dealTicket > 0)
            {
                if(HistoryDealGetInteger(dealTicket, DEAL_POSITION_ID) == ticket)
                {
                    double dealProfit = HistoryDealGetDouble(dealTicket, DEAL_PROFIT);
                    finalProfit += dealProfit;
                }
            }
        }
    }
    else
    {
        // Fallback to maxProfit if history not available
        finalProfit = g_orders[orderIndex].maxProfit;
    }

    isWin = (finalProfit > 0);

    double trailingProfitGained = 0.0;

    // Calculate trailing profit contribution
    if(g_orders[orderIndex].trailingProfitActive && g_orders[orderIndex].trailingUpdateCount > 0)
    {
        trailingProfitGained = g_orders[orderIndex].lockedProfit - g_orders[orderIndex].initialTPTarget;
        trailingProfitGained = MathMax(trailingProfitGained, 0.0);
    }

    // Update chain-specific Anti-Martingale counters
    if(g_orders[orderIndex].orderChainId > 0)
    {
        UpdateChainCounters(g_orders[orderIndex].orderChainId, isWin, finalProfit);
    }
    else
    {
        // Legacy global counter update
        UpdateAntiMartingaleCounters(isWin, finalProfit);
    }

    // Store in recent trades history
    int historyIndex = g_tradeHistoryIndex % 10;
    g_recentTrades[historyIndex].isWin = isWin;
    g_recentTrades[historyIndex].profit = finalProfit;
    g_recentTrades[historyIndex].signalStrength = g_orders[orderIndex].signalStrength;
    g_recentTrades[historyIndex].closeTime = TimeCurrent();
    g_recentTrades[historyIndex].consecutiveWins = g_consecutiveWins;
    g_recentTrades[historyIndex].consecutiveLosses = g_consecutiveLosses;
    g_recentTrades[historyIndex].trailingProfitGained = trailingProfitGained;

    g_tradeHistoryIndex++;

    LogMessage(StringFormat("📊 Trade Closed: Ticket %d, %s, Profit: $%.2f, Trailing Gain: $%.2f",
              g_orders[orderIndex].ticket, isWin ? "WIN" : "LOSS", finalProfit, trailingProfitGained));

    //--- Update enhanced performance tracking
    g_enhancedTradesCount++;
    if(finalProfit > 0)
    {
        g_enhancedWinsCount++;
        g_enhancedTotalProfit += finalProfit;
    }
    else
    {
        g_enhancedTotalLoss += MathAbs(finalProfit);
    }

    //--- Update regime-based performance tracking
    switch(g_currentMarketRegime)
    {
        case MARKET_TRENDING_UP:
        case MARKET_TRENDING_DOWN:
            g_trendingTrades++;
            if(finalProfit > 0)
            {
                g_trendingWins++;
                g_trendingProfit += finalProfit;
            }
            break;

        case MARKET_RANGING:
            g_rangingTrades++;
            if(finalProfit > 0)
            {
                g_rangingWins++;
                g_rangingProfit += finalProfit;
                g_consecutiveRangingLosses = 0; // Reset consecutive losses
            }
            else
            {
                g_consecutiveRangingLosses++;
                g_lastRangingLoss = TimeCurrent();
            }
            break;

        case MARKET_BREAKOUT:
            g_breakoutTrades++;
            if(finalProfit > 0)
            {
                g_breakoutWins++;
                g_breakoutProfit += finalProfit;
            }
            break;
    }

    // Calculate running averages
    double enhancedWinRate = (g_enhancedTradesCount > 0) ? (double)g_enhancedWinsCount / g_enhancedTradesCount * 100.0 : 0.0;
    double enhancedRR = (g_enhancedTotalLoss > 0) ? g_enhancedTotalProfit / g_enhancedTotalLoss : 0.0;

    // Calculate regime-specific win rates
    double trendingWinRate = (g_trendingTrades > 0) ? (double)g_trendingWins / g_trendingTrades * 100.0 : 0.0;
    double rangingWinRate = (g_rangingTrades > 0) ? (double)g_rangingWins / g_rangingTrades * 100.0 : 0.0;
    double breakoutWinRate = (g_breakoutTrades > 0) ? (double)g_breakoutWins / g_breakoutTrades * 100.0 : 0.0;

    LogMessage(StringFormat("📈 ENHANCED PERFORMANCE: Trades=%d, Wins=%d (%.1f%%), R:R=1:%.2f, Avg Signal=%.3f",
               g_enhancedTradesCount, g_enhancedWinsCount, enhancedWinRate, enhancedRR,
               g_enhancedTradesCount > 0 ? g_signalStrengthSum / g_enhancedTradesCount : 0.0));

    LogMessage(StringFormat("📊 REGIME PERFORMANCE: Trending=%.1f%% (%d/%d), Ranging=%.1f%% (%d/%d), Breakout=%.1f%% (%d/%d)",
               trendingWinRate, g_trendingWins, g_trendingTrades,
               rangingWinRate, g_rangingWins, g_rangingTrades,
               breakoutWinRate, g_breakoutWins, g_breakoutTrades));
}

//+------------------------------------------------------------------+
//| Update Anti-Martingale counters based on trade outcome           |
//+------------------------------------------------------------------+
void UpdateAntiMartingaleCounters(bool isWin, double profit)
{


    if(isWin)
    {
        // === WINNING TRADE - ANTI-MARTINGALE LOGIC ===
        g_consecutiveWins++;
        g_consecutiveLosses = 0;
        g_currentWinStreak += profit;
        g_isInWinningStreak = true;

        // Advance to next Anti-Martingale step with maximum limit
        int oldStep = g_currentStep;
        g_currentStep = MathMin(g_consecutiveWins - 1, ArraySize(g_profitSteps) - 1);
        g_currentStep = MathMin(g_currentStep, g_configuredMaxAntiMartingaleStep - 1);

        if(g_currentWinStreak > g_maxWinStreak)
            g_maxWinStreak = g_currentWinStreak;

        LogMessage(StringFormat("WIN: %d consecutive, Step %d, Total: $%.2f",
                  g_consecutiveWins, g_currentStep + 1, g_currentWinStreak));

        // Show next trade targets (FIXED: Use point-based calculation)
        int nextStep = MathMin(g_currentStep + 1, ArraySize(g_profitSteps) - 1);
        string symbol = Symbol();
        double nextProfitPoints = 0, nextLossPoints = 0;

        // Calculate realistic point distances for next trade (UPDATED for BTC)
        if(StringFind(symbol, "BTC") >= 0)
        {
            nextProfitPoints = (5000 + (nextStep * 2000)) * g_profitSteps[nextStep];
            nextLossPoints = (3000 + (nextStep * 1000)) * g_lossSteps[nextStep];
        }
        else if(StringFind(symbol, "XAU") >= 0 || StringFind(symbol, "GOLD") >= 0)
        {
            nextProfitPoints = (150 + (nextStep * 50)) * g_profitSteps[nextStep];
            nextLossPoints = (100 + (nextStep * 30)) * g_lossSteps[nextStep];
        }
        else
        {
            nextProfitPoints = (200 + (nextStep * 50)) * g_profitSteps[nextStep];
            nextLossPoints = (150 + (nextStep * 30)) * g_lossSteps[nextStep];
        }

        double pointValue = GetPointValuePerLot() * InitialLot;
        double nextProfit = nextProfitPoints * pointValue;
        double nextLoss = nextLossPoints * pointValue;

        LogMessage(StringFormat("📈 NEXT TRADE: Step %d, Target Profit: $%.2f (%.0f pts), Target Loss: $%.2f (%.0f pts)",
                  nextStep + 1, nextProfit, nextProfitPoints, nextLoss, nextLossPoints));

        // Profit protection: Take partial profits after 3+ wins
        if(g_consecutiveWins >= 3)
        {
            LogMessage("💰 PROFIT PROTECTION: Consider taking partial profits");
        }
    }
    else
    {
        // === LOSING TRADE - RESET TO BASE ===
        g_consecutiveLosses++;
        g_consecutiveWins = 0;
        g_currentWinStreak = 0.0;
        g_isInWinningStreak = false;
        g_currentStep = 0; // Reset to step 1

        LogMessage(StringFormat("📉 LOSS: Resetting to Step 1. Consecutive losses: %d",
                  g_consecutiveLosses));

        // Show reset targets (FIXED: Use point-based calculation)
        string symbol = Symbol();
        double resetProfitPoints = 0, resetLossPoints = 0;

        // Calculate realistic point distances for reset (step 1) (UPDATED for BTC)
        if(StringFind(symbol, "BTC") >= 0)
        {
            resetProfitPoints = 5000 * g_profitSteps[0];  // Base BTC points
            resetLossPoints = 3000 * g_lossSteps[0];      // Base BTC points
        }
        else if(StringFind(symbol, "XAU") >= 0 || StringFind(symbol, "GOLD") >= 0)
        {
            resetProfitPoints = 150 * g_profitSteps[0];  // Base XAU points
            resetLossPoints = 100 * g_lossSteps[0];      // Base XAU points
        }
        else
        {
            resetProfitPoints = 200 * g_profitSteps[0];  // Base default points
            resetLossPoints = 150 * g_lossSteps[0];      // Base default points
        }

        double pointValue = GetPointValuePerLot() * InitialLot;
        double resetProfit = resetProfitPoints * pointValue;
        double resetLoss = resetLossPoints * pointValue;

        LogMessage(StringFormat("🔄 RESET: Step 1, Target Profit: $%.2f (%.0f pts), Target Loss: $%.2f (%.0f pts)",
                  resetProfit, resetProfitPoints, resetLoss, resetLossPoints));

        // Additional risk management for consecutive losses
        if(g_consecutiveLosses >= 3)
        {
            LogMessage("⚠️ WARNING: 3+ consecutive losses - Consider reducing base lot size");
        }
    }
}

//+------------------------------------------------------------------+
//| Remove order from tracking array                                 |
//+------------------------------------------------------------------+
void RemoveOrderFromTracking(int index)
{
    if(index < 0 || index >= ArraySize(g_orders))
        return;

    // Shift remaining orders down
    for(int i = index; i < ArraySize(g_orders) - 1; i++)
    {
        g_orders[i] = g_orders[i + 1];
    }

    // Resize array
    ArrayResize(g_orders, ArraySize(g_orders) - 1);
    g_activeOrders--;

    LogMessage(StringFormat("📝 Order removed from tracking. Active Orders: %d", g_activeOrders));
}

//+------------------------------------------------------------------+
//| Update daily profit tracking                                     |
//+------------------------------------------------------------------+
void UpdateDailyProfit()
{
    // Check if new day started
    datetime currentTime = TimeCurrent();
    MqlDateTime timeStruct;
    TimeToStruct(currentTime, timeStruct);

    MqlDateTime startStruct;
    TimeToStruct(g_dailyStartTime, startStruct);

    if(timeStruct.day != startStruct.day)
    {
        // New day - reset daily tracking
        g_dailyStartTime = currentTime;
        g_dailyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
        g_dailyProfit = 0.0;

        // Reset Anti-Martingale counters for new day
        g_consecutiveWins = 0;
        g_consecutiveLosses = 0;
        g_currentWinStreak = 0.0;
        g_maxWinStreak = 0.0;
        g_isInWinningStreak = false;
        g_currentStep = 0; // Reset to step 1

        LogMessage("=== NEW TRADING DAY ===");
        LogMessage(StringFormat("Daily start balance: %.2f", g_dailyStartBalance));
        LogMessage("Anti-Martingale counters reset");
    }

    // Calculate current daily profit
    double currentBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    g_dailyProfit = currentBalance - g_dailyStartBalance;
}

//+------------------------------------------------------------------+
//| Check daily loss limit                                           |
//+------------------------------------------------------------------+
bool CheckDailyLossLimit()
{
    if(g_dailyProfit < 0)
    {
        double lossPercentage = MathAbs(g_dailyProfit) / g_dailyStartBalance;
        if(lossPercentage >= MaxDailyLoss)
        {
            LogMessage(StringFormat("DAILY LOSS LIMIT REACHED: %.2f%% (%.2f/%.2f)",
                      lossPercentage * 100, MathAbs(g_dailyProfit), g_dailyStartBalance));
            return true;
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Close all open positions                                         |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
    for(int i = ArraySize(g_orders) - 1; i >= 0; i--)
    {
        if(PositionSelectByTicket(g_orders[i].ticket))
        {
            if(Trade.PositionClose(g_orders[i].ticket))
            {
                LogMessage(StringFormat("Position closed: Ticket %d", g_orders[i].ticket));
            }
            else
            {
                LogMessage(StringFormat("Failed to close position: Ticket %d, Error: %d",
                          g_orders[i].ticket, GetLastError()));
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Calculate Average True Range (ATR)                               |
//+------------------------------------------------------------------+
double CalculateATR()
{
    double high[], low[], close[];
    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    ArraySetAsSeries(close, true);

    int copied = CopyHigh(Symbol(), PERIOD_M1, 0, 14, high);
    if(copied < 14) return 0.0;

    copied = CopyLow(Symbol(), PERIOD_M1, 0, 14, low);
    if(copied < 14) return 0.0;

    copied = CopyClose(Symbol(), PERIOD_M1, 1, 14, close);
    if(copied < 14) return 0.0;

    double atrSum = 0.0;
    for(int i = 0; i < 13; i++)
    {
        double tr1 = high[i] - low[i];
        double tr2 = MathAbs(high[i] - close[i]);
        double tr3 = MathAbs(low[i] - close[i]);
        double tr = MathMax(tr1, MathMax(tr2, tr3));
        atrSum += tr;
    }

    return atrSum / 13.0;
}

//+------------------------------------------------------------------+
//| Calculate Average ATR over longer period                         |
//+------------------------------------------------------------------+
double CalculateAverageATR()
{
    double atrSum = 0.0;
    int validPeriods = 0;

    for(int i = 0; i < 50; i++)
    {
        double high[], low[], close[];
        ArraySetAsSeries(high, true);
        ArraySetAsSeries(low, true);
        ArraySetAsSeries(close, true);

        int copied = CopyHigh(Symbol(), PERIOD_M1, i, 14, high);
        if(copied < 14) continue;

        copied = CopyLow(Symbol(), PERIOD_M1, i, 14, low);
        if(copied < 14) continue;

        copied = CopyClose(Symbol(), PERIOD_M1, i + 1, 14, close);
        if(copied < 14) continue;

        double periodATR = 0.0;
        for(int j = 0; j < 13; j++)
        {
            double tr1 = high[j] - low[j];
            double tr2 = MathAbs(high[j] - close[j]);
            double tr3 = MathAbs(low[j] - close[j]);
            double tr = MathMax(tr1, MathMax(tr2, tr3));
            periodATR += tr;
        }

        atrSum += (periodATR / 13.0);
        validPeriods++;
    }

    return (validPeriods > 0) ? atrSum / validPeriods : 0.0;
}

//+------------------------------------------------------------------+
//| Enhanced logging function                                        |
//+------------------------------------------------------------------+
void LogMessage(string message)
{
    datetime currentTime = TimeCurrent();
    string timeStr = TimeToString(currentTime, TIME_DATE | TIME_SECONDS);
    string fullMessage = StringFormat("[%s] %s", timeStr, message);

    Print(fullMessage);

    // Optional: Write to file for analysis
    int fileHandle = FileOpen("EA_TrailingProfit_Log.txt", FILE_WRITE | FILE_READ | FILE_TXT);
    if(fileHandle != INVALID_HANDLE)
    {
        FileSeek(fileHandle, 0, SEEK_END);
        FileWriteString(fileHandle, fullMessage + "\n");
        FileClose(fileHandle);
    }
}

//+------------------------------------------------------------------+
//| Log current trading status                                       |
//+------------------------------------------------------------------+
void LogTradingStatus()
{
    LogMessage("=== TRADING STATUS UPDATE ===");
    LogMessage(StringFormat("Active Orders: %d/%d", g_activeOrders, MaxSimultaneousOrders));
    LogMessage(StringFormat("Daily P&L: $%.2f (%.2f%%)", g_dailyProfit,
              (g_dailyProfit / g_dailyStartBalance) * 100));
    LogMessage(StringFormat("Anti-Martingale: Step %d, Wins: %d, Losses: %d",
              g_currentStep + 1, g_consecutiveWins, g_consecutiveLosses));
    LogMessage(StringFormat("Win Streak: $%.2f, Max Streak: $%.2f",
              g_currentWinStreak, g_maxWinStreak));
    LogMessage(StringFormat("Trailing Profit: Total Gained: $%.2f, Operations: %d",
              g_totalTrailingProfit, g_trailingProfitCount));

    // Show recent trade performance
    int recentWins = 0, recentTrades = 0;
    double recentTrailingGains = 0.0;

    for(int i = 0; i < 10; i++)
    {
        if(g_recentTrades[i].closeTime > 0)
        {
            recentTrades++;
            if(g_recentTrades[i].isWin) recentWins++;
            recentTrailingGains += g_recentTrades[i].trailingProfitGained;
        }
    }

    if(recentTrades > 0)
    {
        double winRate = (double)recentWins / recentTrades * 100;
        LogMessage(StringFormat("Recent Performance: %d/%d wins (%.1f%%), Trailing Gains: $%.2f",
                  recentWins, recentTrades, winRate, recentTrailingGains));
    }

    //--- Enhanced Signal Quality Status
    LogMessage(StringFormat("📊 Signal Quality: Current=%.3f, Confluence=+%.3f, Market=+%.3f, Volatility=+%.3f",
               g_currentSignalStrength, g_confluenceBoost, g_marketConditionBoost, g_volatilityBoost));

    LogMessage(StringFormat("🎯 Multi-Timeframe: M5=%s, M15=%s, H1=%s, Score=%.3f",
               g_m5TrendAlignment ? "✅" : "❌",
               g_m15TrendAlignment ? "✅" : "❌",
               g_h1TrendAlignment ? "✅" : "❌",
               g_timeframeConfluenceScore));

    LogMessage(StringFormat("⚙️ Dynamic Risk: Lot=%.3f, SL=$%.2f, TP=$%.2f, Interval=%ds",
               g_qualityAdjustedLot, g_adaptiveSLDistance, g_adaptiveTPDistance, g_smartInterval));

    //--- Market Regime Status
    string currentRegimeName = "";
    switch(g_currentMarketRegime)
    {
        case MARKET_TRENDING_UP: currentRegimeName = "📈 TRENDING UP"; break;
        case MARKET_TRENDING_DOWN: currentRegimeName = "📉 TRENDING DOWN"; break;
        case MARKET_RANGING: currentRegimeName = "🔄 RANGING"; break;
        case MARKET_BREAKOUT: currentRegimeName = "🚀 BREAKOUT"; break;
        case MARKET_UNCERTAIN: currentRegimeName = "❓ UNCERTAIN"; break;
        default: currentRegimeName = "❓ UNKNOWN"; break;
    }

    LogMessage(StringFormat("🎯 MARKET REGIME: %s (Confidence: %.2f)", currentRegimeName, g_regimeConfidence));

    if(g_currentRange.is_valid_range)
    {
        double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
        double rangePosition = (currentPrice - g_currentRange.support) / g_currentRange.range_size * 100.0;
        LogMessage(StringFormat("📊 RANGE LEVELS: Support=%.2f, Resistance=%.2f, Size=%.2f, Position=%.1f%%",
                   g_currentRange.support, g_currentRange.resistance, g_currentRange.range_size, rangePosition));
    }

    //--- Regime Performance Summary
    double trendingWinRate = (g_trendingTrades > 0) ? (double)g_trendingWins / g_trendingTrades * 100.0 : 0.0;
    double rangingWinRate = (g_rangingTrades > 0) ? (double)g_rangingWins / g_rangingTrades * 100.0 : 0.0;
    double breakoutWinRate = (g_breakoutTrades > 0) ? (double)g_breakoutWins / g_breakoutTrades * 100.0 : 0.0;

    LogMessage(StringFormat("📈 REGIME WIN RATES: Trending=%.1f%% (%d), Ranging=%.1f%% (%d), Breakout=%.1f%% (%d)",
               trendingWinRate, g_trendingTrades, rangingWinRate, g_rangingTrades, breakoutWinRate, g_breakoutTrades));

    if(g_currentMarketRegime == MARKET_RANGING && g_consecutiveRangingLosses > 0)
    {
        LogMessage(StringFormat("⚠️ RANGING PROTECTION: %d consecutive losses (threshold: %.0f)",
                   g_consecutiveRangingLosses, g_rangingLossThreshold));
    }

    LogMessage("=== END STATUS UPDATE ===");
}

//+------------------------------------------------------------------+
//| Get error description                                            |
//+------------------------------------------------------------------+
string ErrorDescription(int errorCode)
{
    switch(errorCode)
    {
        case 0: return "No error";
        case 4051: return "Invalid function parameter value";
        case 4108: return "Invalid ticket";
        case 4109: return "Trading not allowed";
        case 4110: return "Longs not allowed";
        case 4111: return "Shorts not allowed";
        case 4112: return "Automated trading disabled";
        case 4200: return "Object already exists";
        case 4201: return "Unknown object property";
        case 4202: return "Object does not exist";
        case 4203: return "Unknown object type";
        case 4204: return "No object name";
        case 4205: return "Object coordinates error";
        case 4206: return "No specified subwindow";
        case 10004: return "No money";
        case 10006: return "Request rejected";
        case 10007: return "Request canceled by trader";
        case 10008: return "Order placed";
        case 10009: return "Request done";
        case 10010: return "Request done partially";
        case 10011: return "Request processing error";
        case 10012: return "Request canceled by timeout";
        case 10013: return "Invalid request";
        case 10014: return "Invalid volume";
        case 10015: return "Invalid price";
        case 10016: return "Invalid stops";
        case 10017: return "Trade disabled";
        case 10018: return "Market closed";
        case 10019: return "No money";
        case 10020: return "Prices changed";
        case 10021: return "No quotes";
        case 10022: return "Invalid expiration";
        case 10023: return "Order state changed";
        case 10024: return "Too frequent requests";
        case 10025: return "No changes in request";
        case 10026: return "Autotrading disabled by server";
        case 10027: return "Autotrading disabled by client";
        case 10028: return "Request locked for processing";
        case 10029: return "Order or position frozen";
        case 10030: return "Invalid order filling type";
        case 10031: return "No connection";
        case 10032: return "Only real accounts allowed";
        case 10033: return "Limit exceeded";
        case 10034: return "Invalid or expired pending order";
        case 10035: return "Position with specified POSITION_IDENTIFIER already closed";
        default: return StringFormat("Unknown error %d", errorCode);
    }
}

//+------------------------------------------------------------------+
//| Adaptive Pause Mechanism Functions                               |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Check if adaptive pause should be triggered                      |
//+------------------------------------------------------------------+
void CheckAdaptivePause()
{
    datetime currentTime = TimeCurrent();

    // Check if currently paused
    if(g_isPaused)
    {
        if(currentTime >= g_pauseEndTime)
        {
            ResumeFromPause();
        }
        return;
    }

    // Check for pause triggers
    bool shouldPause = false;
    string pauseReason = "";

    // Trigger 1: Consecutive losses
    if(g_consecutiveLosses >= MaxConsecutiveLosses)
    {
        shouldPause = true;
        pauseReason = StringFormat("Consecutive losses: %d", g_consecutiveLosses);
    }

    // Trigger 2: High volatility with losses
    if(g_consecutiveLosses >= 2)
    {
        double currentATR = CalculateATR();
        double avgATR = CalculateAverageATR();
        if(avgATR > 0 && (currentATR / avgATR) > PauseVolatilityThreshold)
        {
            shouldPause = true;
            pauseReason = StringFormat("High volatility (%.2fx) with %d losses",
                         currentATR / avgATR, g_consecutiveLosses);
        }
    }

    // Trigger 3: Significant drawdown from peak
    if(g_maxWinStreak > 50 && (g_maxWinStreak - g_currentWinStreak) > (g_maxWinStreak * 0.3))
    {
        shouldPause = true;
        pauseReason = StringFormat("30%% drawdown from peak ($%.2f to $%.2f)",
                     g_maxWinStreak, g_currentWinStreak);
    }

    if(shouldPause)
    {
        ActivatePause(pauseReason);
    }
}

//+------------------------------------------------------------------+
//| Activate pause mechanism                                         |
//+------------------------------------------------------------------+
void ActivatePause(string reason)
{
    g_isPaused = true;
    g_pauseStartTime = TimeCurrent();
    g_pauseEndTime = g_pauseStartTime + (PauseMinutes * 60);
    g_pauseCount++;
    g_prePauseBalance = AccountInfoDouble(ACCOUNT_BALANCE);

    // Store original settings
    g_originalMinConfluence = MinConfluenceSignals;
    g_originalMinTrendStrength = MinTrendStrength;

    // Close all open positions during pause
    CloseAllPositions();

    LogMessage("=== ADAPTIVE PAUSE ACTIVATED ===");
    LogMessage(StringFormat("🛑 REASON: %s", reason));
    LogMessage(StringFormat("⏰ DURATION: %d minutes", PauseMinutes));
    LogMessage(StringFormat("📊 PAUSE COUNT: %d", g_pauseCount));
    LogMessage(StringFormat("💰 BALANCE: $%.2f", g_prePauseBalance));
    LogMessage(StringFormat("🔄 RESUME TIME: %s", TimeToString(g_pauseEndTime)));
}

//+------------------------------------------------------------------+
//| Resume from pause with enhanced settings                         |
//+------------------------------------------------------------------+
void ResumeFromPause()
{
    g_isPaused = false;
    double currentBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    double balanceChange = currentBalance - g_prePauseBalance;

    // Reset signal thresholds if enabled
    if(ResetSignalThresholds)
    {
        // Temporarily increase confluence requirements for safer re-entry
        g_currentMinConfluence = MathMin(g_originalMinConfluence + 1, 4);
        g_currentMinTrendStrength = MathMin(g_originalMinTrendStrength + 0.1, 0.6);

        LogMessage("🔧 ENHANCED FILTERS: Confluence +1, Trend Strength +0.1 for next 10 trades");
    }

    LogMessage("=== PAUSE COMPLETED - TRADING RESUMED ===");
    LogMessage(StringFormat("⏰ PAUSE DURATION: %d minutes", PauseMinutes));
    LogMessage(StringFormat("💰 BALANCE CHANGE: $%.2f", balanceChange));
    LogMessage(StringFormat("🎯 ENHANCED FILTERS: %s", ResetSignalThresholds ? "ACTIVE" : "DISABLED"));
    LogMessage("🚀 READY FOR TRADING");
}

//+------------------------------------------------------------------+
//| Reset enhanced filters after successful trades                   |
//+------------------------------------------------------------------+
void CheckResetEnhancedFilters()
{
    static int tradesAfterPause = 0;

    if(ResetSignalThresholds && g_currentMinConfluence > g_originalMinConfluence)
    {
        tradesAfterPause++;

        // Reset after 10 successful trades or 5 wins
        if(tradesAfterPause >= 10 || g_consecutiveWins >= 5)
        {
            g_currentMinConfluence = g_originalMinConfluence;
            g_currentMinTrendStrength = g_originalMinTrendStrength;
            tradesAfterPause = 0;

            LogMessage("🔧 ENHANCED FILTERS RESET: Back to normal confluence requirements");
        }
    }
}