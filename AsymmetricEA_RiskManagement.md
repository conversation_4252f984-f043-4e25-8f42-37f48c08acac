# Enhanced Risk Management for Asymmetric 1:3 EA

## Core Risk Management Principles

### 1. Position Sizing Limits
```mql5
// Maximum risk per trade: 1.5% of account balance
#define MAX_RISK_PER_TRADE 0.015

// Maximum total exposure: 5% of account balance  
#define MAX_TOTAL_EXPOSURE 0.05

// Maximum simultaneous positions: 2 (reduced from 3 for asymmetric)
#define MAX_ASYMMETRIC_POSITIONS 2
```

### 2. Account Balance Requirements
```mql5
// Minimum account balance for asymmetric system
#define MIN_ACCOUNT_BALANCE 5000.0

// Recommended account balance for optimal performance
#define RECOMMENDED_BALANCE 10000.0

// Emergency stop balance (50% of minimum)
#define EMERGENCY_STOP_BALANCE 2500.0
```

### 3. Circuit Breaker System
```mql5
struct CircuitBreaker
{
    int consecutiveLossLimit;      // 3 consecutive losses
    double dailyLossLimit;         // 15% of account balance
    double weeklyLossLimit;        // 25% of account balance
    int dailyTradeLimit;           // 20 trades per day
    bool emergencyStopActivated;   // Emergency stop flag
};
```

## Anti-Martingale Protection Enhanced

### 1. Graduated Stop Loss Protection
```mql5
double CalculateProtectedSL(int consecutiveWins, double lastProfit, double winStreak)
{
    double baseSL = 18.0; // Base asymmetric SL
    
    switch(consecutiveWins)
    {
        case 0:
            return baseSL; // First trade: full SL allowed
            
        case 1:
            return MathMin(baseSL, lastProfit * 1.5); // 1.5x last profit
            
        case 2:
            return MathMin(baseSL, lastProfit * 0.95); // 95% of last profit
            
        default:
            return MathMin(baseSL, winStreak * 0.25); // 25% of total streak
    }
}
```

### 2. Dynamic Risk Scaling
```mql5
double CalculateDynamicLotSize(double baseRisk, int consecutiveWins)
{
    double riskMultiplier = 1.0;
    
    // Reduce risk after losses
    if(g_asymmetricRisk.consecutiveAsymmetricLosses > 0)
    {
        riskMultiplier = 1.0 - (g_asymmetricRisk.consecutiveAsymmetricLosses * 0.2);
        riskMultiplier = MathMax(riskMultiplier, 0.4); // Minimum 40% of base risk
    }
    
    // Increase risk slightly after wins (capped)
    if(consecutiveWins > 0)
    {
        riskMultiplier = 1.0 + (consecutiveWins * 0.1);
        riskMultiplier = MathMin(riskMultiplier, 1.3); // Maximum 130% of base risk
    }
    
    return baseRisk * riskMultiplier;
}
```

## Time-Based Risk Controls

### 1. Progressive Risk Reduction
```mql5
struct TimeBasedRisk
{
    double fiveMinuteRiskReduction;    // 10% risk reduction if not profitable at 5 min
    double tenMinuteRiskReduction;     // 20% risk reduction if not profitable at 10 min
    double fifteenMinuteRiskReduction; // 30% risk reduction if not profitable at 15 min
    bool forceCloseAt20Minutes;        // Mandatory close at 20 minutes
};
```

### 2. Momentum-Based Risk Adjustment
```mql5
double AdjustRiskForMomentum(double baseRisk, double momentumStrength)
{
    // Reduce risk in low momentum conditions
    if(momentumStrength < 0.3)
    {
        return baseRisk * 0.7; // 30% risk reduction
    }
    
    // Standard risk in normal momentum
    if(momentumStrength < 0.7)
    {
        return baseRisk;
    }
    
    // Slightly increase risk in strong momentum (capped)
    return baseRisk * 1.1; // 10% risk increase, maximum
}
```

## Market Regime Risk Adaptation

### 1. Regime-Specific Risk Limits
```mql5
void ApplyRegimeRiskLimits(ENUM_MARKET_REGIME regime)
{
    switch(regime)
    {
        case MARKET_TRENDING:
            g_asymmetricRisk.maxRiskPerTrade = 0.015; // Standard 1.5%
            g_asymmetricRisk.maxTotalExposure = 0.05; // Standard 5%
            break;
            
        case MARKET_RANGING:
            g_asymmetricRisk.maxRiskPerTrade = 0.012; // Reduced to 1.2%
            g_asymmetricRisk.maxTotalExposure = 0.04; // Reduced to 4%
            break;
            
        case MARKET_VOLATILE:
            g_asymmetricRisk.maxRiskPerTrade = 0.010; // Reduced to 1.0%
            g_asymmetricRisk.maxTotalExposure = 0.03; // Reduced to 3%
            break;
            
        case MARKET_UNCERTAIN:
            g_asymmetricRisk.maxRiskPerTrade = 0.008; // Reduced to 0.8%
            g_asymmetricRisk.maxTotalExposure = 0.025; // Reduced to 2.5%
            break;
    }
}
```

### 2. Volatility-Based Adjustments
```mql5
double CalculateVolatilityAdjustedRisk(double baseRisk, double currentATR, double avgATR)
{
    double volatilityRatio = currentATR / avgATR;
    
    // High volatility: reduce risk
    if(volatilityRatio > 1.5)
    {
        return baseRisk * 0.7;
    }
    
    // Low volatility: slightly increase risk
    if(volatilityRatio < 0.7)
    {
        return baseRisk * 1.1;
    }
    
    // Normal volatility: standard risk
    return baseRisk;
}
```

## Emergency Procedures

### 1. Automatic System Shutdown
```mql5
void CheckEmergencyConditions()
{
    double currentBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    double currentEquity = AccountInfoDouble(ACCOUNT_EQUITY);
    
    // Emergency stop if balance drops below threshold
    if(currentBalance < EMERGENCY_STOP_BALANCE)
    {
        LogMessage("EMERGENCY STOP: Account balance below minimum threshold");
        g_asymmetricRisk.emergencyStopActivated = true;
        CloseAllPositions();
        return;
    }
    
    // Emergency stop if equity drops significantly
    if(currentEquity < currentBalance * 0.85) // 15% equity drawdown
    {
        LogMessage("EMERGENCY STOP: Equity drawdown exceeds 15%");
        g_asymmetricRisk.emergencyStopActivated = true;
        CloseAllPositions();
        return;
    }
    
    // Emergency stop after excessive consecutive losses
    if(g_asymmetricRisk.consecutiveAsymmetricLosses >= 5)
    {
        LogMessage("EMERGENCY STOP: 5 consecutive losses reached");
        g_asymmetricRisk.emergencyStopActivated = true;
        CloseAllPositions();
        return;
    }
}
```

### 2. Recovery Procedures
```mql5
void InitiateRecoveryMode()
{
    // Reduce risk to minimum levels
    g_asymmetricRisk.maxRiskPerTrade = 0.005; // 0.5%
    g_asymmetricRisk.maxTotalExposure = 0.015; // 1.5%
    g_asymmetricRisk.maxDailyTrades = 5; // Limit to 5 trades
    
    // Require manual confirmation for trades
    g_requireManualConfirmation = true;
    
    LogMessage("RECOVERY MODE ACTIVATED: Reduced risk parameters in effect");
}
```

## Monitoring and Alerts

### 1. Real-Time Risk Monitoring
```mql5
void MonitorRiskMetrics()
{
    double currentRisk = CalculateCurrentAsymmetricExposure();
    double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    double riskPercentage = currentRisk / accountBalance;
    
    // Alert levels
    if(riskPercentage > 0.04) // 80% of maximum exposure
    {
        LogMessage(StringFormat("HIGH RISK WARNING: Current exposure %.2f%% approaching limit", 
                  riskPercentage * 100));
    }
    
    if(g_asymmetricRisk.consecutiveAsymmetricLosses >= 2)
    {
        LogMessage(StringFormat("LOSS STREAK WARNING: %d consecutive losses", 
                  g_asymmetricRisk.consecutiveAsymmetricLosses));
    }
}
```

### 2. Performance Tracking
```mql5
struct RiskPerformanceMetrics
{
    double maxRiskTaken;           // Highest risk percentage taken
    double avgRiskPerTrade;        // Average risk per trade
    int riskViolations;            // Number of risk limit violations
    double maxDrawdownPeriod;      // Longest drawdown period
    double recoveryTime;           // Average recovery time from losses
};
```

## Safety Measures Implementation

### 1. Pre-Trade Risk Validation
```mql5
bool ValidateTradeRisk(double lotSize, double slAmount, int orderType)
{
    // Check account balance
    if(AccountInfoDouble(ACCOUNT_BALANCE) < MIN_ACCOUNT_BALANCE)
        return false;
    
    // Check emergency stop status
    if(g_asymmetricRisk.emergencyStopActivated)
        return false;
    
    // Check risk per trade
    if(!ValidateAsymmetricRisk(lotSize, slAmount))
        return false;
    
    // Check total exposure
    double totalRisk = CalculateCurrentAsymmetricExposure() + (lotSize * slAmount);
    if(totalRisk > AccountInfoDouble(ACCOUNT_BALANCE) * g_asymmetricRisk.maxTotalExposure)
        return false;
    
    // Check consecutive losses
    if(g_asymmetricRisk.consecutiveAsymmetricLosses >= 3)
        return false;
    
    return true;
}
```

### 2. Post-Trade Risk Assessment
```mql5
void AssessPostTradeRisk(int orderIndex, bool isWin, double profit)
{
    if(isWin)
    {
        g_asymmetricRisk.consecutiveAsymmetricLosses = 0;
        LogMessage(StringFormat("RISK RESET: Win recorded, consecutive losses reset"));
    }
    else
    {
        g_asymmetricRisk.consecutiveAsymmetricLosses++;
        LogMessage(StringFormat("RISK INCREASE: Loss recorded, consecutive losses: %d", 
                  g_asymmetricRisk.consecutiveAsymmetricLosses));
        
        // Check if circuit breaker should activate
        if(g_asymmetricRisk.consecutiveAsymmetricLosses >= 3)
        {
            LogMessage("CIRCUIT BREAKER ACTIVATED: 3 consecutive losses reached");
            // Temporarily disable new trades
            g_asymmetricRisk.asymmetricSystemEnabled = false;
        }
    }
    
    // Update risk tracking
    g_asymmetricRisk.totalAsymmetricRisk = CalculateCurrentAsymmetricExposure();
    g_asymmetricRisk.lastAsymmetricTrade = TimeCurrent();
}
